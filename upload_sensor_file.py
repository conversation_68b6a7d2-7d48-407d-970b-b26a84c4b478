#!/usr/bin/env python3
"""
Upload sensor_25ms_no_timestamp.py to NodeMCU as main.py
"""
import serial
import time
import sys

def upload_file_to_nodemcu(port, source_file, target_file='main.py'):
    """
    Upload a Python file to NodeMCU using paste mode
    """
    try:
        print(f"Connecting to {port}...")
        ser = serial.Serial(port, 115200, timeout=10)
        time.sleep(3)  # Wait for connection to stabilize
        
        # Send Ctrl+C to interrupt any running program
        print("Interrupting any running program...")
        ser.write(b'\x03')
        time.sleep(1)
        
        # Clear any pending data
        ser.read_all()
        
        # Send Enter to get prompt
        ser.write(b'\r\n')
        time.sleep(1)
        
        # Read the file content
        print(f"Reading {source_file}...")
        with open(source_file, 'r', encoding='utf-8') as f:
            file_content = f.read()
        
        # Enter paste mode (Ctrl+E)
        print("Entering paste mode...")
        ser.write(b'\x05')
        time.sleep(1)
        
        # Read paste mode response
        response = ser.read_all().decode('utf-8', errors='ignore')
        print("Paste mode response:", repr(response))
        
        # Create the file writing command
        file_write_command = f"""
with open('{target_file}', 'w') as f:
    f.write('''{file_content}''')
print('File {target_file} uploaded successfully')
"""
        
        print(f"Uploading {source_file} as {target_file}...")
        
        # Send the file content
        ser.write(file_write_command.encode('utf-8'))
        
        # Exit paste mode (Ctrl+D)
        ser.write(b'\x04')
        time.sleep(3)
        
        # Read the execution response
        response = ser.read_all().decode('utf-8', errors='ignore')
        print("Upload response:", response)
        
        # Verify the file was created
        print("Verifying file upload...")
        ser.write(b'import os; print(os.listdir())\r\n')
        time.sleep(2)
        
        response = ser.read_all().decode('utf-8', errors='ignore')
        print("File list:", response)
        
        # Check file size
        ser.write(f"import os; print('File size:', os.stat('{target_file}')[6])\r\n".encode('utf-8'))
        time.sleep(2)
        
        response = ser.read_all().decode('utf-8', errors='ignore')
        print("File info:", response)
        
        ser.close()
        print(f"Successfully uploaded {source_file} to NodeMCU as {target_file}")
        return True
        
    except Exception as e:
        print(f"Error uploading file: {e}")
        return False

if __name__ == "__main__":
    port = "COM14"
    source_file = "sensor_25ms_no_timestamp.py"
    target_file = "main.py"  # This will auto-run on boot
    
    print(f"Uploading {source_file} to NodeMCU...")
    print("This will overwrite any existing main.py file on the device.")
    
    if upload_file_to_nodemcu(port, source_file, target_file):
        print("\n✅ Upload completed successfully!")
        print("The sensor program will now run automatically when NodeMCU boots.")
        print("You can reset the NodeMCU to start the sensor program.")
    else:
        print("\n❌ Upload failed!")
