# MPU6050 센서 기반 드럼 타격 감지 시스템 (25ms 샘플링 버전)

이 프로젝트는 NodeMCU V3(ESP8266)와 TCA9548A I2C 멀티플렉서를 사용하여 최대 8개의 MPU6050 가속도 센서를 연결하고, 드럼 타격을 감지하여 키보드 입력을 시뮬레이션하는 시스템입니다. 각 센서의 데이터는 실시간 그래프로 표시되며, 타격 감지 임계값을 조절할 수 있습니다.

## 25ms 샘플링 속도 지원

이 버전은 기존 50ms 샘플링 속도에서 **25ms 샘플링 속도**로 개선되어 더 빠른 데이터 수집과 반응성을 제공합니다:

- 초당 40회 샘플링 (40Hz)
- 더 정확한 타격 감지
- 더 부드러운 그래프 표시
- 낮은 지연 시간

## 하드웨어 가속 지원

이 프로젝트는 두 가지 버전의 그래픽 인터페이스를 제공합니다:

1. **일반 버전 (Matplotlib 기반)**: 기본적인 그래프 표시 기능을 제공합니다.
2. **하드웨어 가속 버전 (PyQtGraph 기반)**: OpenGL을 통한 하드웨어 가속을 지원하여 더 빠른 그래프 업데이트와 부드러운 화면 표시를 제공합니다.

하드웨어 가속 버전은 다음과 같은 장점이 있습니다:
- 더 빠른 그래프 업데이트 속도 (일반 버전보다 약 10-100배 빠름)
- 낮은 CPU 사용량
- 부드러운 애니메이션
- 더 나은 반응성

## 하드웨어 구성

### 필요한 부품
- NodeMCU V3 (ESP8266 기반)
- TCA9548A I2C 멀티플렉서
- MPU6050 가속도 센서 (최대 8개)
- 점퍼 와이어
- USB 케이블 (NodeMCU 연결용)

### 연결 방법
1. **NodeMCU와 TCA9548A 연결**
   - NodeMCU D1 (GPIO 5) → TCA9548A SCL
   - NodeMCU D2 (GPIO 4) → TCA9548A SDA
   - NodeMCU 3.3V → TCA9548A VCC
   - NodeMCU GND → TCA9548A GND
   - TCA9548A A0, A1, A2 → GND (멀티플렉서 주소를 0x70으로 설정)

2. **TCA9548A와 MPU6050 센서 연결**
   - 각 MPU6050 센서는 TCA9548A의 SC0~SC7, SD0~SD7 핀에 연결
   - 각 센서마다 4개의 선 연결 필요: VCC, GND, SCL, SDA
   - 모든 센서의 VCC는 3.3V에 연결
   - 모든 센서의 GND는 GND에 연결

## 소프트웨어 설정

### 필요한 소프트웨어
- Python 3.x
- pySerial 라이브러리 (`pip install pyserial`)
- MicroPython 펌웨어 (ESP8266_GENERIC-20250415-v1.25.0.bin 또는 최신 버전)

### 펌웨어 설치 방법
1. NodeMCU를 USB 케이블로 컴퓨터에 연결합니다.
2. 장치 관리자에서 NodeMCU의 COM 포트 번호를 확인합니다.
3. esptool.py를 사용하여 MicroPython 펌웨어를 설치합니다:
   ```
   pip install esptool
   esptool.py --port COM포트번호 --baud 115200 write_flash --flash_size=detect 0 ESP8266_GENERIC-20250415-v1.25.0.bin
   ```

## 프로젝트 파일 설명

### 1. upload_file.py
- 컴퓨터에서 NodeMCU로 파일을 업로드하는 Python 스크립트
- 사용법: `python upload_file.py 소스파일 대상파일`
- 예: `python upload_file.py all_sensors_display.py main.py`

### 2. simple_sensor_monitor.py
- 기본적인 센서 모니터링 스크립트
- 연결된 센서를 감지하고 Z축 가속도 데이터를 100ms 간격으로 읽어 표시

### 3. sensor_50ms.py
- 50ms 간격으로 센서 데이터를 읽는 스크립트
- 초당 20회 샘플링 속도

### 4. all_sensors_display.py
- NodeMCU용 최종 버전 스크립트
- 8개의 센서 데이터를 모두 표시 (연결되지 않은 센서는 0으로 표시)
- 50ms 간격으로 데이터 업데이트
- 타임스탬프와 함께 데이터 표시

### 5. drum_sensor_gui.py
- PC용 그래픽 인터페이스 프로그램 (Matplotlib 기반)
- 8개 센서 데이터를 실시간 그래프로 표시
- 각 센서별 임계값 조절 기능
- 타격 감지 시 시각적 표시 및 키보드 입력 시뮬레이션
- 백그라운드 모드 지원 (메모장 등에 입력 가능)

### 6. drum_sensor_pyqtgraph.py
- PC용 그래픽 인터페이스 프로그램 (PyQtGraph 기반, 하드웨어 가속 지원)
- OpenGL을 통한 하드웨어 가속으로 더 빠른 그래프 업데이트
- 8개 센서 데이터를 실시간 그래프로 표시
- 각 센서별 임계값 조절 기능
- 타격 감지 시 시각적 표시 및 키보드 입력 시뮬레이션
- 백그라운드 모드 지원 (메모장 등에 입력 가능)

### 7. sensor_25ms.py
- 25ms 샘플링 속도로 센서 데이터를 읽는 최적화된 스크립트
- 초당 40회 샘플링 (40Hz)으로 더 정확한 타격 감지
- 센서 초기화 및 안정화 시간 최적화
- 채널 전환 지연 시간 최소화

### 8. improved_sensors_display.py
- 센서 감지 및 초기화 과정이 개선된 스크립트
- 더 안정적인 센서 감지 및 데이터 수집
- 센서 연결 상태 자세한 표시

### 9. install_requirements.py
- 필요한 라이브러리 자동 설치 스크립트
- 실행 파일(.exe) 생성 기능 포함

### 10. build_pyqtgraph_exe.py
- 하드웨어 가속 버전 실행 파일 생성 스크립트
- PyQtGraph 및 OpenGL 라이브러리 설치

## 사용 방법

### 1. NodeMCU 코드 업로드
1. NodeMCU를 USB 케이블로 컴퓨터에 연결합니다.
2. 다음 명령어로 코드를 업로드합니다:
   ```
   python upload_file.py all_sensors_display.py main.py
   ```
   - 이렇게 하면 NodeMCU가 부팅될 때마다 자동으로 코드가 실행됩니다.

### 2. 필요한 라이브러리 설치
1. 다음 명령어로 필요한 라이브러리를 설치합니다:
   ```
   python install_requirements.py
   ```
   - 이 스크립트는 필요한 모든 라이브러리를 자동으로 설치합니다.
   - 실행 파일(.exe) 생성 여부를 묻는 메시지가 표시되면 'y'를 입력하여 생성할 수 있습니다.

### 3. 그래픽 인터페이스 실행
1. 일반 버전 (Matplotlib 기반):
   ```
   python drum_sensor_gui.py
   ```
   - 또는 실행 파일을 생성한 경우 `dist/DrumSensorMonitor.exe`를 실행합니다.

2. 하드웨어 가속 버전 (PyQtGraph 기반):
   ```
   python drum_sensor_pyqtgraph.py
   ```
   - 또는 하드웨어 가속 실행 파일을 생성한 경우 `dist/DrumSensorMonitor_HW_Accel.exe`를 실행합니다.
   - 하드웨어 가속 버전은 다음 명령어로 빌드할 수 있습니다:
   ```
   python build_pyqtgraph_exe.py
   ```

### 4. 25ms 샘플링 속도 버전 사용
1. 25ms 샘플링 속도 버전을 NodeMCU에 업로드:
   ```
   python upload_file.py sensor_25ms.py main.py
   ```
   - 이 명령어는 25ms 샘플링 속도 스크립트를 NodeMCU의 메인 파일로 업로드합니다.

2. 시리얼 모니터로 데이터 확인:
   ```
   python -m serial.tools.miniterm COM포트번호 115200
   ```
   - 25ms 간격으로 데이터가 출력되는지 확인합니다.

3. 그래픽 인터페이스 사용법:
   - 시리얼 포트 선택: NodeMCU가 연결된 COM 포트를 선택합니다.
   - 연결 버튼: 클릭하여 NodeMCU와 연결합니다.
   - 임계값 조절: 각 센서의 타격 감지 임계값을 슬라이더로 조절합니다.
   - 백그라운드 모드: 체크하면 타격 감지 시 키보드 입력이 시뮬레이션됩니다.

4. 백그라운드 모드 사용법:
   - 백그라운드 모드를 체크합니다.
   - 메모장 등의 텍스트 편집기를 엽니다.
   - 드럼을 타격하면 해당 센서 번호(1-8)가 텍스트 편집기에 입력됩니다.

### 5. 직접 데이터 모니터링 (선택 사항)
1. 시리얼 모니터를 사용하여 NodeMCU의 출력을 직접 확인할 수 있습니다:
   ```
   python -m serial.tools.miniterm COM포트번호 115200
   ```
   - 예: `python -m serial.tools.miniterm COM13 115200`

2. 출력 형식:
   ```
   타임스탬프(ms),센서1,센서2,센서3,센서4,센서5,센서6,센서7,센서8
   ```
   - 타임스탬프: 시작 후 경과 시간(밀리초)
   - 센서1~8: 각 센서의 Z축 가속도 값(g 단위)
   - 연결되지 않은 센서는 0.000으로 표시됩니다.

## 장시간 실행 시 고려사항

### 안정성 개선 방법
1. **주기적인 I2C 재초기화**:
   ```python
   # 예: 1시간마다 I2C 버스 재초기화
   if elapsed_ms % 3600000 == 0:  # 1시간마다
       i2c = I2C(scl=Pin(I2C_SCL_PIN), sda=Pin(I2C_SDA_PIN), freq=400000)
       print("I2C bus reinitialized")
   ```

2. **워치독 타이머 활용**:
   ```python
   # 워치독 타이머 설정 (예: 10초)
   from machine import WDT
   wdt = WDT(timeout=10000)  # 10초

   # 메인 루프에서 주기적으로 워치독 리셋
   while True:
       # 기존 코드...
       wdt.feed()  # 워치독 타이머 리셋
   ```

3. **오류 로깅 추가**:
   ```python
   # 오류 발생 시 로그 기록
   error_count = 0
   # 메인 루프에서
   try:
       # 센서 읽기 코드...
   except Exception as e:
       error_count += 1
       print(f"Error occurred: {e}, count: {error_count}")
       if error_count > 100:  # 오류가 너무 많이 발생하면 재시작
           machine.reset()
   ```

## 문제 해결

### 센서가 감지되지 않는 경우
1. 모든 연결을 확인합니다.
2. TCA9548A의 A0, A1, A2 핀이 GND에 연결되어 있는지 확인합니다.
3. I2C 주소가 올바른지 확인합니다 (TCA9548A: 0x70, MPU6050: 0x68).
4. 센서에 전원이 공급되는지 확인합니다 (3.3V와 GND 연결).

### 데이터가 불안정한 경우
1. 전원 공급이 안정적인지 확인합니다.
2. 와이어 길이가 너무 길지 않은지 확인합니다 (I2C 통신은 짧은 거리에서 더 안정적).
3. I2C 주파수를 낮춰볼 수 있습니다 (예: 400kHz에서 100kHz로).

## 라이센스
이 프로젝트는 MIT 라이센스 하에 배포됩니다.
