#!/usr/bin/env python3
"""
Test different I2C frequencies to find optimal speed for sensor detection
"""
import serial
import time

def test_i2c_frequencies():
    try:
        print("I2C 주파수별 센서 감지 테스트...")
        ser = serial.Serial("COM14", 115200, timeout=15)
        time.sleep(2)
        
        # 프로그램 중단
        for i in range(3):
            ser.write(b'\x03')
            time.sleep(0.5)
        
        ser.read_all()
        
        # 테스트할 주파수들 (낮은 순서부터)
        frequencies = [10000, 20000, 50000, 100000, 200000, 400000]
        
        print("=== I2C 주파수별 센서 감지 테스트 ===")
        
        for freq in frequencies:
            print(f"\n--- {freq}Hz 테스트 ---")
            
            # I2C 초기화
            init_commands = [
                "from machine import I2C, Pin",
                f"i2c = I2C(scl=Pin(5), sda=Pin(4), freq={freq})",
                f"print('I2C initialized at {freq}Hz')",
                "import time"
            ]
            
            for cmd in init_commands:
                ser.write(f"{cmd}\r\n".encode('utf-8'))
                time.sleep(0.5)
            
            response = ser.read_all().decode('utf-8', errors='ignore')
            print(f"초기화: {response.strip()}")
            
            # 전체 I2C 스캔
            ser.write(b'devices = i2c.scan()\r\n')
            time.sleep(1)
            ser.write(b'print(f"All devices at {freq}Hz:", [hex(d) for d in devices])\r\n')
            time.sleep(2)
            
            response = ser.read_all().decode('utf-8', errors='ignore')
            print(f"전체 스캔: {response.strip()}")
            
            # 각 채널별 센서 개수 확인
            channel_test_commands = [
                "sensor_count = 0",
                "for channel in range(8):",
                "    try:",
                "        i2c.writeto(0x70, bytes([1 << channel]))",
                "        time.sleep_ms(100)",
                "        ch_devices = i2c.scan()",
                "        if 0x68 in ch_devices:",
                "            sensor_count += 1",
                "            print(f'Channel {channel}: MPU6050 found')",
                "    except:",
                "        pass",
                f"print(f'Total MPU6050 at {freq}Hz: {{sensor_count}}')"
            ]
            
            for cmd in channel_test_commands:
                ser.write(f"{cmd}\r\n".encode('utf-8'))
                time.sleep(0.2)
            
            time.sleep(3)
            response = ser.read_all().decode('utf-8', errors='ignore')
            
            # 응답 파싱
            lines = response.split('\n')
            sensor_count = 0
            found_channels = []
            
            for line in lines:
                line = line.strip()
                if line and not line.startswith('>>>') and not line.startswith('...'):
                    print(f"  {line}")
                    if "Channel" in line and "MPU6050 found" in line:
                        try:
                            channel_num = int(line.split("Channel")[1].split(":")[0].strip())
                            found_channels.append(channel_num)
                        except:
                            pass
                    elif "Total MPU6050" in line:
                        try:
                            sensor_count = int(line.split(":")[-1].strip())
                        except:
                            pass
            
            print(f"📊 {freq}Hz 결과: {sensor_count}개 센서, 채널 {found_channels}")
            
            # 잠시 대기
            time.sleep(2)
        
        print("\n=== 최적 주파수 찾기 ===")
        print("위 결과를 보고 가장 많은 센서가 감지된 주파수를 확인하세요.")
        
        ser.close()
        
    except Exception as e:
        print(f"오류: {e}")

if __name__ == "__main__":
    test_i2c_frequencies()
