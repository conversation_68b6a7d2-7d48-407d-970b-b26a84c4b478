"""
Simple script to read and display sensor data from the NodeMCU
"""
import serial
import time
import serial.tools.list_ports

def list_serial_ports():
    """List all available serial ports"""
    ports = serial.tools.list_ports.comports()
    print("Available serial ports:")
    for i, port in enumerate(ports):
        print(f"{i+1}. {port.device} - {port.description}")
    return [port.device for port in ports]

def read_sensor_data(port, baud_rate=115200, timeout=10):
    """Read sensor data from the specified port"""
    try:
        # Open serial port
        ser = serial.Serial(port, baud_rate, timeout=1)
        print(f"Connected to {port} at {baud_rate} baud")
        print("Reading data (press Ctrl+C to stop)...")
        print("Waiting for data...")
        
        # Set a timeout for the entire operation
        start_time = time.time()
        
        # Read data until timeout or keyboard interrupt
        while time.time() - start_time < timeout:
            if ser.in_waiting:
                line = ser.readline().decode('utf-8', errors='ignore').strip()
                if line:
                    print(line)
                    # Reset timeout if we got data
                    start_time = time.time()
            time.sleep(0.01)  # Small delay to prevent CPU hogging
            
        print(f"No data received for {timeout} seconds. Exiting.")
        
    except KeyboardInterrupt:
        print("Stopped by user")
    except Exception as e:
        print(f"Error: {str(e)}")
    finally:
        if 'ser' in locals() and ser.is_open:
            ser.close()
            print(f"Connection to {port} closed")

if __name__ == "__main__":
    # List available ports
    ports = list_serial_ports()
    
    if not ports:
        print("No serial ports found. Please check your connections.")
        exit(1)
    
    # Ask user to select a port
    try:
        selection = int(input(f"Select a port (1-{len(ports)}): "))
        if 1 <= selection <= len(ports):
            selected_port = ports[selection-1]
            read_sensor_data(selected_port)
        else:
            print("Invalid selection")
    except ValueError:
        print("Please enter a valid number")
    except KeyboardInterrupt:
        print("\nExiting...")
