"""
Sensor program optimized for 1kHz I2C frequency
Ultra-low speed for maximum sensor detection and stability
"""
import time
from machine import I2C, Pin, freq
import gc

# Configuration - Optimized for 1kHz I2C
I2C_SCL_PIN = 5  # D1 on NodeMCU
I2C_SDA_PIN = 4  # D2 on NodeMCU
TCA9548A_ADDRESS = 0x70
MPU6050_ADDRESS = 0x68
SAMPLE_RATE_MS = 25
NUM_SENSORS = 8

# 1kHz optimized timing
I2C_FREQUENCY = 1000         # 1kHz - ultra slow for stability
CHANNEL_SWITCH_DELAY = 500   # 500ms for channel switching
INIT_DELAY = 1000           # 1 second for initialization
SCAN_DELAY = 2000           # 2 seconds for scanning

def safe_print(msg):
    try:
        print(msg)
    except:
        pass

def setup_1khz():
    """Setup I2C at 1kHz for maximum stability"""
    try:
        # Conservative CPU frequency
        freq(80000000)
        safe_print("CPU frequency set to 80MHz")
        
        # Initialize I2C at 1kHz
        safe_print("Initializing I2C at 1kHz for maximum stability...")
        i2c = I2C(scl=Pin(I2C_SCL_PIN), sda=Pin(I2C_SDA_PIN), freq=I2C_FREQUENCY)
        
        # Long stabilization delay
        time.sleep_ms(3000)
        safe_print("I2C stabilized")
        
        # Test scan
        devices = i2c.scan()
        safe_print(f"I2C devices found: {[hex(d) for d in devices]}")
        
        return i2c
    except Exception as e:
        safe_print(f"Setup error: {e}")
        return None

def select_channel_1khz(i2c, channel):
    """Channel selection optimized for 1kHz"""
    try:
        # Multiple attempts with long delays
        for attempt in range(5):
            try:
                # Disable all channels first
                i2c.writeto(TCA9548A_ADDRESS, b'\x00')
                time.sleep_ms(200)
                
                # Select target channel
                i2c.writeto(TCA9548A_ADDRESS, bytes([1 << channel]))
                time.sleep_ms(CHANNEL_SWITCH_DELAY)
                
                return True
            except Exception as e:
                safe_print(f"Channel {channel} selection attempt {attempt + 1} failed: {e}")
                time.sleep_ms(300)
        
        return False
    except Exception as e:
        safe_print(f"Channel selection error: {e}")
        return False

def initialize_mpu6050_1khz(i2c, channel):
    """MPU6050 initialization optimized for 1kHz"""
    try:
        safe_print(f"Initializing MPU6050 on channel {channel} at 1kHz...")
        
        # Select channel
        if not select_channel_1khz(i2c, channel):
            safe_print(f"Failed to select channel {channel}")
            return False
        
        # Check if sensor is present
        devices = i2c.scan()
        if MPU6050_ADDRESS not in devices:
            safe_print(f"No MPU6050 on channel {channel}")
            return False
        
        safe_print(f"MPU6050 detected on channel {channel}")
        
        # Wake up with multiple attempts
        for attempt in range(5):
            try:
                # Device reset
                i2c.writeto_mem(MPU6050_ADDRESS, 0x6B, b'\x80')
                time.sleep_ms(INIT_DELAY)
                
                # Wake up
                i2c.writeto_mem(MPU6050_ADDRESS, 0x6B, b'\x00')
                time.sleep_ms(INIT_DELAY)
                
                break
            except Exception as e:
                safe_print(f"Wake attempt {attempt + 1} failed: {e}")
                time.sleep_ms(500)
        else:
            safe_print(f"Failed to wake up sensor on channel {channel}")
            return False
        
        # Configure sensor
        try:
            # Set accelerometer range
            i2c.writeto_mem(MPU6050_ADDRESS, 0x1C, b'\x00')  # ±2g
            time.sleep_ms(INIT_DELAY)
            
            # Set sample rate
            i2c.writeto_mem(MPU6050_ADDRESS, 0x19, b'\x07')  # Slower rate
            time.sleep_ms(500)
            
            # Set DLPF
            i2c.writeto_mem(MPU6050_ADDRESS, 0x1A, b'\x06')  # Lowest bandwidth
            time.sleep_ms(500)
            
        except Exception as e:
            safe_print(f"Configuration failed: {e}")
        
        # Verify WHO_AM_I
        try:
            who_am_i = i2c.readfrom_mem(MPU6050_ADDRESS, 0x75, 1)
            safe_print(f"Channel {channel} WHO_AM_I: {hex(who_am_i[0])}")
        except Exception as e:
            safe_print(f"WHO_AM_I read failed: {e}")
        
        safe_print(f"✅ Channel {channel} initialized successfully!")
        return True
        
    except Exception as e:
        safe_print(f"Initialization failed for channel {channel}: {e}")
        return False

def read_accel_z_1khz(i2c, channel):
    """Read accelerometer at 1kHz with error handling"""
    try:
        # Select channel
        if not select_channel_1khz(i2c, channel):
            return 0.0
        
        # Read Z-axis data
        data = i2c.readfrom_mem(MPU6050_ADDRESS, 0x3F, 2)
        z = (data[0] << 8) | data[1]
        if z > 32767:
            z -= 65536
        z_g = z / 16384.0
        return z_g
    except:
        return 0.0

def main():
    """Main function with 1kHz I2C optimization"""
    try:
        safe_print("=== 1kHz I2C Sensor Program ===")
        safe_print("Ultra-low frequency for maximum sensor detection")
        
        # Setup
        i2c = setup_1khz()
        if i2c is None:
            safe_print("Failed to initialize I2C")
            return
        
        # Check TCA9548A
        devices = i2c.scan()
        if TCA9548A_ADDRESS not in devices:
            safe_print("TCA9548A not found")
            return
        
        safe_print("TCA9548A found, starting sensor detection...")
        
        # Initialize sensors
        connected_sensors = [False] * NUM_SENSORS
        
        safe_print("\n=== 1kHz Sensor Initialization ===")
        for channel in range(NUM_SENSORS):
            safe_print(f"\n--- Channel {channel} ---")
            
            if initialize_mpu6050_1khz(i2c, channel):
                connected_sensors[channel] = True
                safe_print(f"✅ Sensor {channel + 1} ready!")
            else:
                safe_print(f"❌ Sensor {channel + 1} failed!")
            
            # Delay between sensors
            time.sleep_ms(3000)
            gc.collect()
        
        # Status report
        total_connected = sum(connected_sensors)
        safe_print(f"\n=== Final Status ===")
        safe_print(f"Total sensors at 1kHz: {total_connected}")
        
        for i, connected in enumerate(connected_sensors):
            status = "✅ Connected" if connected else "❌ Not Connected"
            safe_print(f"Sensor {i+1} (Channel {i}): {status}")
        
        if total_connected == 0:
            safe_print("No sensors detected at 1kHz")
            return
        
        # Start data output
        safe_print(f"\nStarting data output at {SAMPLE_RATE_MS}ms intervals...")
        safe_print("sensor1,sensor2,sensor3,sensor4,sensor5,sensor6,sensor7,sensor8")
        
        last_read_time = time.ticks_ms()
        
        while True:
            current_time = time.ticks_ms()
            if time.ticks_diff(current_time, last_read_time) >= SAMPLE_RATE_MS:
                last_read_time = current_time
                
                values = []
                for channel in range(NUM_SENSORS):
                    if connected_sensors[channel]:
                        z_g = read_accel_z_1khz(i2c, channel)
                    else:
                        z_g = 0.0
                    values.append(f"{z_g:.3f}")
                
                print(f"{','.join(values)}")
                
                if current_time % 10000 < SAMPLE_RATE_MS:
                    gc.collect()
            else:
                time.sleep_ms(3)
                
    except KeyboardInterrupt:
        safe_print("Stopped by user")
    except Exception as e:
        safe_print(f"Main error: {e}")

if __name__ == "__main__":
    main()
