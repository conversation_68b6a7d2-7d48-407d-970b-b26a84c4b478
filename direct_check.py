#!/usr/bin/env python3
"""
Direct check of NodeMCU file
"""
import serial
import time

def direct_check(port):
    try:
        ser = serial.Serial(port, 115200, timeout=10)
        time.sleep(3)
        
        # Stop any running program
        ser.write(b'\x03\x03')
        time.sleep(1)
        ser.read_all()
        
        # Simple commands one by one
        commands = [
            ('File list', b'import os\r\n'),
            ('', b'print(os.listdir())\r\n'),
            ('File size', b'print(os.stat("main.py")[6])\r\n'),
            ('File exists', b'print("main.py" in os.listdir())\r\n'),
        ]
        
        for desc, cmd in commands:
            if desc:
                print(f"\n{desc}:")
            ser.write(cmd)
            time.sleep(2)
            response = ser.read_all().decode('utf-8', errors='ignore')
            # Clean up the response
            lines = response.split('\n')
            for line in lines:
                line = line.strip()
                if line and not line.startswith('>>>') and not line == cmd.decode().strip():
                    print(f"  {line}")
        
        # Try to read a small part of the file
        print("\nReading file header:")
        ser.write(b'f = open("main.py")\r\n')
        time.sleep(1)
        ser.write(b'print(f.read(100))\r\n')
        time.sleep(2)
        ser.write(b'f.close()\r\n')
        time.sleep(1)
        
        response = ser.read_all().decode('utf-8', errors='ignore')
        lines = response.split('\n')
        for line in lines:
            line = line.strip()
            if line and not line.startswith('>>>') and 'f.' not in line and 'print(' not in line:
                print(f"  {line}")
        
        ser.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    print("=== Direct NodeMCU File Check ===")
    direct_check("COM14")
