#!/usr/bin/env python3
"""
Hard reset and check sensor program
"""
import serial
import time

def hard_reset_test(port):
    try:
        print("하드 리셋 후 센서 프로그램 확인...")
        ser = serial.Serial(port, 115200, timeout=15)
        time.sleep(2)
        
        # 하드 리셋 (DTR/RTS 사용)
        print("하드 리셋 실행...")
        ser.setDTR(False)
        ser.setRTS(True)
        time.sleep(0.1)
        ser.setRTS(False)
        time.sleep(2)
        
        # 부팅 메시지 읽기
        print("부팅 메시지 읽는 중...")
        start_time = time.time()
        boot_messages = []
        
        while time.time() - start_time < 10:
            if ser.in_waiting > 0:
                try:
                    line = ser.readline().decode('utf-8', errors='ignore').strip()
                    if line:
                        boot_messages.append(line)
                        print(f"  {line}")
                        
                        # 센서 데이터 형식 감지
                        if ',' in line and line.count(',') == 7:
                            try:
                                values = line.split(',')
                                if len(values) == 8:
                                    print("✅ 센서 데이터 감지!")
                                    # 몇 줄 더 읽기
                                    for _ in range(5):
                                        if ser.in_waiting > 0:
                                            line = ser.readline().decode('utf-8', errors='ignore').strip()
                                            if line:
                                                print(f"  {line}")
                                        time.sleep(0.1)
                                    break
                            except:
                                pass
                except:
                    pass
            time.sleep(0.1)
        
        print(f"\n총 {len(boot_messages)}개의 부팅 메시지를 받았습니다.")
        
        # 중요한 메시지 찾기
        important_keywords = [
            "CPU frequency",
            "I2C devices",
            "TCA9548A",
            "MPU6050",
            "sensor1,sensor2",
            "initialized"
        ]
        
        found_keywords = []
        for keyword in important_keywords:
            for msg in boot_messages:
                if keyword in msg:
                    found_keywords.append(keyword)
                    break
        
        print(f"발견된 키워드: {found_keywords}")
        
        if len(found_keywords) >= 3:
            print("✅ 센서 프로그램이 정상적으로 실행되고 있습니다!")
        else:
            print("⚠️ 센서 프로그램이 실행되지 않았을 수 있습니다.")
        
        ser.close()
        
    except Exception as e:
        print(f"오류: {e}")

if __name__ == "__main__":
    hard_reset_test("COM14")
