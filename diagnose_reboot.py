#!/usr/bin/env python3
"""
Diagnose NodeMCU reboot issue
"""
import serial
import time

def diagnose_reboot(port):
    try:
        print("NodeMCU 리부팅 문제 진단 중...")
        ser = serial.Serial(port, 115200, timeout=10)
        time.sleep(2)
        
        # 인터럽트 시도
        print("프로그램 중단 시도...")
        for i in range(5):
            ser.write(b'\x03')  # Ctrl+C
            time.sleep(0.5)
            if ser.in_waiting > 0:
                response = ser.read_all().decode('utf-8', errors='ignore')
                print(f"응답 {i+1}: {repr(response)}")
                if '>>>' in response:
                    print("✅ 프롬프트 확보!")
                    break
        else:
            print("❌ 프롬프트를 얻을 수 없습니다.")
            
        # main.py 파일을 다른 이름으로 변경하여 자동 실행 방지
        print("\nmain.py 파일을 임시로 비활성화...")
        try:
            ser.write(b'import os\r\n')
            time.sleep(1)
            ser.write(b'os.rename("main.py", "main_backup.py")\r\n')
            time.sleep(2)
            response = ser.read_all().decode('utf-8', errors='ignore')
            print(f"파일 이름 변경 응답: {repr(response)}")
            
            # 리부팅
            print("소프트 리부팅...")
            ser.write(b'import machine\r\n')
            time.sleep(1)
            ser.write(b'machine.reset()\r\n')
            time.sleep(3)
            
            # 리부팅 후 상태 확인
            response = ser.read_all().decode('utf-8', errors='ignore')
            print(f"리부팅 후 상태: {repr(response)}")
            
        except Exception as e:
            print(f"파일 이름 변경 오류: {e}")
        
        ser.close()
        
    except Exception as e:
        print(f"진단 오류: {e}")

if __name__ == "__main__":
    diagnose_reboot("COM14")
