#!/usr/bin/env python3
"""
Test 8 sensors initialization
"""
import serial
import time

def test_8_sensors():
    try:
        print("8개 센서 초기화 테스트...")
        ser = serial.Serial("COM14", 115200, timeout=20)
        time.sleep(3)
        
        print("초기화 과정 모니터링...")
        start_time = time.time()
        messages = []
        sensor_status = {}
        
        while time.time() - start_time < 60:  # 1분간 모니터링
            if ser.in_waiting > 0:
                try:
                    line = ser.readline().decode('utf-8', errors='ignore').strip()
                    if line:
                        messages.append(line)
                        print(f"  {line}")
                        
                        # 센서 초기화 상태 추적
                        if "Sensor" in line and "initialized successfully" in line:
                            # Extract sensor number
                            parts = line.split()
                            for i, part in enumerate(parts):
                                if part == "Sensor" and i + 1 < len(parts):
                                    sensor_num = parts[i + 1]
                                    sensor_status[sensor_num] = "Success"
                                    break
                        
                        elif "Failed to initialize sensor" in line:
                            # Extract sensor number
                            parts = line.split()
                            for i, part in enumerate(parts):
                                if part == "sensor" and i + 1 < len(parts):
                                    sensor_num = parts[i + 1]
                                    sensor_status[sensor_num] = "Failed"
                                    break
                        
                        # 센서 데이터 감지
                        if ',' in line and line.count(',') == 7:
                            try:
                                values = line.split(',')
                                if len(values) == 8:
                                    print("✅ 센서 데이터 수신 시작!")
                                    # 몇 줄 더 읽기
                                    for _ in range(5):
                                        if ser.in_waiting > 0:
                                            line = ser.readline().decode('utf-8', errors='ignore').strip()
                                            if line:
                                                print(f"  {line}")
                                        time.sleep(0.1)
                                    break
                            except:
                                pass
                except:
                    pass
            time.sleep(0.1)
        
        print(f"\n=== 초기화 결과 요약 ===")
        print(f"총 {len(messages)}개의 메시지를 받았습니다.")
        print(f"센서 초기화 상태: {sensor_status}")
        
        # 성공한 센서 개수 계산
        success_count = sum(1 for status in sensor_status.values() if status == "Success")
        print(f"성공적으로 초기화된 센서: {success_count}개")
        
        # 중요한 키워드 찾기
        keywords = ["CPU frequency", "I2C devices", "TCA9548A", "Total connected sensors"]
        found = []
        for keyword in keywords:
            for msg in messages:
                if keyword in msg:
                    found.append(msg)
                    break
        
        print(f"\n중요한 정보:")
        for info in found:
            print(f"  {info}")
        
        ser.close()
        
    except Exception as e:
        print(f"오류: {e}")

if __name__ == "__main__":
    test_8_sensors()
