"""
Safe version of sensor script with enhanced error handling
Script to read Z-axis data from up to 8 MPU6050 sensors through TCA9548A multiplexer
Displays data for all 8 sensors, showing 0 for unconnected sensors
Optimized for 25ms sampling rate with improved sensor detection
Modified to remove timestamp for better graph compatibility
"""
import time
from machine import I2C, Pin, freq
import gc

# Configuration
I2C_SCL_PIN = 5  # D1 on NodeMCU
I2C_SDA_PIN = 4  # D2 on NodeMCU
TCA9548A_ADDRESS = 0x70  # Default address of TCA9548A
MPU6050_ADDRESS = 0x68   # Default address of MPU6050
SAMPLE_RATE_MS = 25      # How often to read sensor data (in milliseconds)
NUM_SENSORS = 8          # Total number of sensors to support
SCAN_DELAY_MS = 100      # Delay for sensor scanning (in milliseconds)
INIT_DELAY_MS = 50       # Delay after sensor initialization (in milliseconds)
CHANNEL_SWITCH_DELAY_MS = 5  # Delay after channel switching (in milliseconds)

def safe_print(msg):
    """Safe print function with error handling"""
    try:
        print(msg)
    except:
        pass

def safe_setup():
    """Safe setup with error handling"""
    try:
        # Try to increase CPU frequency for better performance
        try:
            freq(160000000)  # Set to 160MHz for faster processing
            safe_print("CPU frequency set to 160MHz")
        except:
            safe_print("Could not set CPU frequency")

        # Initialize I2C with error handling
        try:
            i2c = I2C(scl=Pin(I2C_SCL_PIN), sda=Pin(I2C_SDA_PIN), freq=400000)
            safe_print("I2C initialized successfully")
            return i2c
        except Exception as e:
            safe_print(f"I2C initialization failed: {e}")
            return None
            
    except Exception as e:
        safe_print(f"Setup error: {e}")
        return None

# Function to select TCA9548A channel with error handling
def select_channel(i2c, channel):
    """Select TCA9548A channel with error handling"""
    try:
        if not 0 <= channel <= 7:
            return False
        i2c.writeto(TCA9548A_ADDRESS, bytes([1 << channel]))
        time.sleep_ms(CHANNEL_SWITCH_DELAY_MS)
        return True
    except:
        return False

# Function to read Z-axis acceleration from MPU6050 with error handling
def read_accel_z(i2c, channel):
    """Read Z-axis acceleration with enhanced error handling"""
    try:
        # Select the channel
        if not select_channel(i2c, channel):
            return 0.0
        
        # Read Z-axis acceleration registers directly
        data = i2c.readfrom_mem(MPU6050_ADDRESS, 0x3F, 2)
        
        # Convert to 16-bit signed value
        z = (data[0] << 8) | data[1]
        if z > 32767:
            z -= 65536
        
        # Convert to g (±2g range)
        z_g = z / 16384.0
        
        return z_g
    except:
        # Return 0 if sensor is not connected or error occurs
        return 0.0

# Function to initialize MPU6050 sensor with error handling
def initialize_mpu6050(i2c, channel):
    """Initialize MPU6050 with enhanced error handling"""
    try:
        if not select_channel(i2c, channel):
            return False
        
        # Wake up the MPU6050 (in case it's in sleep mode)
        i2c.writeto_mem(MPU6050_ADDRESS, 0x6B, b'\x00')
        time.sleep_ms(INIT_DELAY_MS)
        
        # Set accelerometer range to ±2g
        i2c.writeto_mem(MPU6050_ADDRESS, 0x1C, b'\x00')
        time.sleep_ms(INIT_DELAY_MS)
        
        # Set sample rate divider for faster sampling
        i2c.writeto_mem(MPU6050_ADDRESS, 0x19, b'\x00')
        
        # Set digital low pass filter to minimum
        i2c.writeto_mem(MPU6050_ADDRESS, 0x1A, b'\x01')
        
        # Verify that the sensor is responding
        who_am_i = i2c.readfrom_mem(MPU6050_ADDRESS, 0x75, 1)
        if who_am_i[0] != 0x68:
            safe_print(f"Warning: Unexpected WHO_AM_I value on channel {channel}: {hex(who_am_i[0])}")
            return False
        
        return True
    except Exception as e:
        safe_print(f"Error initializing sensor on channel {channel}: {e}")
        return False

def main():
    """Main function with comprehensive error handling"""
    try:
        safe_print("Starting sensor program...")
        
        # Safe setup
        i2c = safe_setup()
        if i2c is None:
            safe_print("Failed to initialize I2C. Exiting.")
            return
        
        # Scan I2C bus
        try:
            devices = i2c.scan()
            safe_print(f"I2C devices found: {[hex(device) for device in devices]}")
        except Exception as e:
            safe_print(f"I2C scan error: {e}")
            devices = []
        
        # Check if TCA9548A is found
        if TCA9548A_ADDRESS not in devices:
            safe_print("TCA9548A not found. Check your wiring.")
        else:
            safe_print("TCA9548A found at address 0x70")
        
        # Scan each channel for MPU6050 sensors
        connected_sensors = [False] * NUM_SENSORS
        
        safe_print("Scanning for sensors...")
        for channel in range(NUM_SENSORS):
            try:
                if select_channel(i2c, channel):
                    time.sleep_ms(SCAN_DELAY_MS)
                    
                    channel_devices = i2c.scan()
                    safe_print(f"Channel {channel} devices: {[hex(device) for device in channel_devices]}")
                    
                    if MPU6050_ADDRESS in channel_devices:
                        safe_print(f"MPU6050 found on channel {channel}, initializing...")
                        if initialize_mpu6050(i2c, channel):
                            connected_sensors[channel] = True
                            safe_print(f"MPU6050 on channel {channel} initialized successfully")
                        else:
                            safe_print(f"Failed to initialize MPU6050 on channel {channel}")
            except Exception as e:
                safe_print(f"Error scanning channel {channel}: {e}")
        
        # Display connection status for all sensors
        safe_print("\nSensor connection status:")
        for i, connected in enumerate(connected_sensors):
            status = "Connected" if connected else "Not Connected"
            safe_print(f"Sensor {i+1} status: {status}")
        
        # Count connected sensors
        num_connected = sum(connected_sensors)
        safe_print(f"\nTotal connected sensors: {num_connected}")
        
        if num_connected == 0:
            safe_print("No sensors connected. Check your wiring and try again.")
        else:
            # Continuously read and display Z-axis data
            safe_print(f"\nReading Z-axis acceleration data at {SAMPLE_RATE_MS}ms intervals (Ctrl+C to stop)...")
            safe_print("sensor1,sensor2,sensor3,sensor4,sensor5,sensor6,sensor7,sensor8")
            
            # For timing precision
            last_read_time = time.ticks_ms()
            
            try:
                while True:
                    current_time = time.ticks_ms()
                    # Only read if it's time to do so (every 25ms)
                    if time.ticks_diff(current_time, last_read_time) >= SAMPLE_RATE_MS:
                        last_read_time = current_time
                        
                        # Read Z-axis data from all sensors
                        values = []
                        for channel in range(NUM_SENSORS):
                            if connected_sensors[channel]:
                                # Read data from connected sensor
                                z_g = read_accel_z(i2c, channel)
                            else:
                                # Use 0 for unconnected sensor
                                z_g = 0.0
                            
                            values.append(f"{z_g:.3f}")
                        
                        # Print sensor values
                        try:
                            print(f"{','.join(values)}")
                        except:
                            pass
                        
                        # Periodic garbage collection
                        if current_time % 1000 < SAMPLE_RATE_MS:
                            gc.collect()
                    else:
                        # Yield to other tasks for a very short time
                        time.sleep_ms(1)
            except KeyboardInterrupt:
                safe_print("Stopped by user")
            except Exception as e:
                safe_print(f"Runtime error: {e}")
                
    except Exception as e:
        safe_print(f"Main function error: {e}")
    
    safe_print("Program ended")

# Run the main function
if __name__ == "__main__":
    main()
