#!/usr/bin/env python3
"""
Monitor 10ms high-speed sensor operation
"""
import serial
import time

def monitor_10ms_operation():
    try:
        print("=== 10ms 고속 센서 모니터링 ===")
        print("100Hz 샘플링 속도 확인 중...")
        ser = serial.Serial("COM14", 115200, timeout=15)
        time.sleep(3)
        
        print("고속 센서 프로그램 모니터링 중...")
        start_time = time.time()
        messages = []
        data_count = 0
        last_data_time = time.time()
        data_times = []
        
        while time.time() - start_time < 60:  # 1분간 모니터링
            if ser.in_waiting > 0:
                try:
                    line = ser.readline().decode('utf-8', errors='ignore').strip()
                    if line:
                        messages.append(line)
                        current_time = time.time()
                        
                        # 초기화 메시지들
                        if "10ms High-Speed Sensor Program" in line:
                            print("🚀 10ms 고속 센서 프로그램 시작!")
                            
                        elif "CPU frequency set to 160MHz" in line:
                            print("⚡ CPU 최대 속도 (160MHz) 설정")
                            
                        elif "I2C initialized at" in line:
                            freq = line.split("at")[1].split("Hz")[0].strip()
                            print(f"🔗 I2C {freq}Hz로 초기화")
                            
                        elif "Total sensors ready:" in line:
                            count = line.split(":")[1].strip()
                            print(f"📊 준비된 센서: {count}")
                            
                        elif "Starting 10ms (100Hz) data output" in line:
                            print("📈 10ms (100Hz) 데이터 출력 시작!")
                            last_data_time = current_time
                            
                        elif "sensor1,sensor2,sensor3" in line:
                            print("📋 센서 데이터 헤더 확인")
                            
                        # 센서 데이터 분석
                        elif ',' in line and line.count(',') == 7:
                            try:
                                values = line.split(',')
                                if len(values) == 8:
                                    data_count += 1
                                    data_times.append(current_time)
                                    
                                    # 처음 몇 개 데이터 표시
                                    if data_count <= 10:
                                        non_zero_count = sum(1 for v in values if float(v) != 0.0)
                                        print(f"📊 데이터 {data_count}: {non_zero_count}/8 센서 활성")
                                        print(f"    값: {line}")
                                    
                                    # 주기적으로 속도 계산
                                    elif data_count % 100 == 0:
                                        if len(data_times) >= 100:
                                            # 최근 100개 샘플의 평균 속도 계산
                                            recent_times = data_times[-100:]
                                            time_span = recent_times[-1] - recent_times[0]
                                            if time_span > 0:
                                                actual_rate = 99 / time_span  # 99 intervals for 100 samples
                                                print(f"⚡ 실제 속도: {actual_rate:.1f}Hz (목표: 100Hz)")
                                                
                                                # 속도 평가
                                                if actual_rate >= 95:
                                                    print("✅ 우수한 속도 성능!")
                                                elif actual_rate >= 80:
                                                    print("⚠️ 보통 속도 성능")
                                                else:
                                                    print("❌ 속도 성능 부족")
                                    
                                    # 1000개 데이터 후 종료
                                    if data_count >= 1000:
                                        print(f"📈 1000개 샘플 수집 완료!")
                                        break
                                        
                            except Exception as e:
                                pass
                        
                        # 성능 모니터링 메시지
                        elif "Performance:" in line and "Hz" in line:
                            print(f"📊 {line}")
                            
                        elif "Final performance:" in line:
                            print(f"🏁 {line}")
                            
                        else:
                            # 기타 중요한 메시지들
                            if any(keyword in line for keyword in [
                                "ready for high-speed", "failed", "error", "Performance"
                            ]):
                                print(f"  {line}")
                        
                except Exception as e:
                    pass
            time.sleep(0.001)  # 1ms sleep for high-speed monitoring
        
        # 최종 분석
        print(f"\n=== 10ms 고속 센서 분석 결과 ===")
        print(f"총 메시지: {len(messages)}개")
        print(f"수집된 데이터: {data_count}개")
        
        if len(data_times) >= 10:
            # 전체 평균 속도 계산
            total_time = data_times[-1] - data_times[0]
            if total_time > 0:
                average_rate = (len(data_times) - 1) / total_time
                print(f"평균 샘플링 속도: {average_rate:.1f}Hz")
                
                # 목표 대비 성능
                target_rate = 100.0
                performance_ratio = (average_rate / target_rate) * 100
                print(f"목표 대비 성능: {performance_ratio:.1f}%")
                
                if performance_ratio >= 95:
                    print("🏆 10ms 고속 모드 성공!")
                elif performance_ratio >= 80:
                    print("✅ 10ms 모드 양호")
                elif performance_ratio >= 60:
                    print("⚠️ 10ms 모드 보통")
                else:
                    print("❌ 10ms 모드 성능 부족")
            
            # 타이밍 안정성 분석
            if len(data_times) >= 100:
                intervals = []
                for i in range(1, min(101, len(data_times))):
                    interval = data_times[i] - data_times[i-1]
                    intervals.append(interval * 1000)  # Convert to ms
                
                avg_interval = sum(intervals) / len(intervals)
                min_interval = min(intervals)
                max_interval = max(intervals)
                
                print(f"\n타이밍 분석 (최근 100개 샘플):")
                print(f"  평균 간격: {avg_interval:.1f}ms (목표: 10ms)")
                print(f"  최소 간격: {min_interval:.1f}ms")
                print(f"  최대 간격: {max_interval:.1f}ms")
                print(f"  간격 변동: {max_interval - min_interval:.1f}ms")
                
                if abs(avg_interval - 10.0) <= 1.0:
                    print("✅ 타이밍 정확도 우수")
                elif abs(avg_interval - 10.0) <= 2.0:
                    print("⚠️ 타이밍 정확도 보통")
                else:
                    print("❌ 타이밍 정확도 부족")
        
        else:
            print("⚠️ 충분한 데이터를 수집하지 못했습니다.")
        
        ser.close()
        
    except Exception as e:
        print(f"오류: {e}")

if __name__ == "__main__":
    monitor_10ms_operation()
