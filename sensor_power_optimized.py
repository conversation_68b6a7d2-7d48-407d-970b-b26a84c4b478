"""
Power-optimized sensor script for 8 MPU6050 sensors
Reduces power consumption and improves initialization reliability
"""
import time
from machine import I2C, Pin, freq
import gc

# Configuration
I2C_SCL_PIN = 5  # D1 on NodeMCU
I2C_SDA_PIN = 4  # D2 on NodeMCU
TCA9548A_ADDRESS = 0x70  # Default address of TCA9548A
MPU6050_ADDRESS = 0x68   # Default address of MPU6050
SAMPLE_RATE_MS = 25      # How often to read sensor data (in milliseconds)
NUM_SENSORS = 8          # Total number of sensors to support
SCAN_DELAY_MS = 300      # Increased delay for sensor scanning
INIT_DELAY_MS = 200      # Increased delay after sensor initialization
CHANNEL_SWITCH_DELAY_MS = 50  # Increased delay after channel switching
POWER_SETTLE_DELAY_MS = 500   # Delay for power settling

def safe_print(msg):
    """Safe print function with error handling"""
    try:
        print(msg)
    except:
        pass

def safe_setup():
    """Safe setup with power optimization"""
    try:
        # Set lower CPU frequency to reduce power consumption
        try:
            freq(80000000)  # Set to 80MHz to reduce power consumption
            safe_print("CPU frequency set to 80MHz for power saving")
        except Exception as e:
            safe_print(f"Could not set CPU frequency: {e}")

        # Initialize I2C with lower frequency for stability
        try:
            i2c = I2C(scl=Pin(I2C_SCL_PIN), sda=Pin(I2C_SDA_PIN), freq=50000)  # Very low frequency for stability
            safe_print("I2C initialized at 50kHz for maximum stability")
            
            # Allow I2C to stabilize
            time.sleep_ms(200)
            
            # Test I2C communication
            devices = i2c.scan()
            safe_print(f"I2C scan successful: {[hex(d) for d in devices]}")
            
            return i2c
        except Exception as e:
            safe_print(f"I2C initialization failed: {e}")
            return None
            
    except Exception as e:
        safe_print(f"Setup error: {e}")
        return None

def select_channel(i2c, channel):
    """Select TCA9548A channel with power-aware error handling"""
    try:
        if not 0 <= channel <= 7:
            return False
        
        # Multiple attempts with longer delays
        for attempt in range(5):
            try:
                channel_byte = 1 << channel
                i2c.writeto(TCA9548A_ADDRESS, bytes([channel_byte]))
                time.sleep_ms(CHANNEL_SWITCH_DELAY_MS)
                return True
            except Exception as e:
                safe_print(f"Channel {channel} selection attempt {attempt + 1} failed: {e}")
                time.sleep_ms(100)  # Wait before retry
        
        return False
    except Exception as e:
        safe_print(f"Channel selection error for channel {channel}: {e}")
        return False

def power_on_sensor(i2c, channel):
    """Power on and initialize sensor with extended delays"""
    try:
        safe_print(f"Powering on sensor on channel {channel}...")
        
        # Select channel
        if not select_channel(i2c, channel):
            safe_print(f"Failed to select channel {channel}")
            return False
        
        # Allow power to settle
        time.sleep_ms(POWER_SETTLE_DELAY_MS)
        
        # Check if sensor is present
        devices = i2c.scan()
        if MPU6050_ADDRESS not in devices:
            safe_print(f"No MPU6050 found on channel {channel}")
            return False
        
        safe_print(f"MPU6050 detected on channel {channel}, initializing...")
        
        # Step 1: Wake up with multiple attempts and longer delays
        wake_success = False
        for attempt in range(10):  # More attempts
            try:
                i2c.writeto_mem(MPU6050_ADDRESS, 0x6B, b'\x00')
                time.sleep_ms(INIT_DELAY_MS)
                wake_success = True
                break
            except Exception as e:
                safe_print(f"Wake attempt {attempt + 1} failed on channel {channel}: {e}")
                time.sleep_ms(200)  # Longer wait between attempts
        
        if not wake_success:
            safe_print(f"Failed to wake up sensor on channel {channel}")
            return False
        
        # Step 2: Configure sensor with minimal settings to reduce power
        try:
            # Set accelerometer range to ±2g
            i2c.writeto_mem(MPU6050_ADDRESS, 0x1C, b'\x00')
            time.sleep_ms(INIT_DELAY_MS)
            
            # Set sample rate to lower frequency to reduce power
            i2c.writeto_mem(MPU6050_ADDRESS, 0x19, b'\x07')  # Slower sample rate
            time.sleep_ms(100)
            
            # Set digital low pass filter
            i2c.writeto_mem(MPU6050_ADDRESS, 0x1A, b'\x06')  # Lowest bandwidth
            time.sleep_ms(100)
            
            # Disable gyroscope to save power
            i2c.writeto_mem(MPU6050_ADDRESS, 0x6C, b'\x07')  # Disable gyro
            time.sleep_ms(100)
            
        except Exception as e:
            safe_print(f"Configuration failed on channel {channel}: {e}")
            # Continue anyway
        
        # Step 3: Verify sensor (optional)
        try:
            who_am_i = i2c.readfrom_mem(MPU6050_ADDRESS, 0x75, 1)
            safe_print(f"Channel {channel} WHO_AM_I: {hex(who_am_i[0])}")
        except Exception as e:
            safe_print(f"WHO_AM_I read failed on channel {channel}: {e}")
        
        safe_print(f"Sensor on channel {channel} initialized successfully!")
        return True
        
    except Exception as e:
        safe_print(f"Error powering on sensor on channel {channel}: {e}")
        return False

def read_accel_z(i2c, channel):
    """Read Z-axis acceleration with power-aware error handling"""
    try:
        # Select channel with retry
        for attempt in range(3):
            if select_channel(i2c, channel):
                break
            time.sleep_ms(20)
        else:
            return 0.0

        # Small delay after channel selection
        time.sleep_ms(10)

        # Read Z-axis acceleration registers
        data = i2c.readfrom_mem(MPU6050_ADDRESS, 0x3F, 2)
        
        # Convert to 16-bit signed value
        z = (data[0] << 8) | data[1]
        if z > 32767:
            z -= 65536
        
        # Convert to g (±2g range)
        z_g = z / 16384.0
        
        return z_g
    except Exception as e:
        return 0.0

def main():
    """Main function with power-optimized initialization"""
    try:
        safe_print("Starting power-optimized 8-sensor program...")
        
        # Safe setup
        i2c = safe_setup()
        if i2c is None:
            safe_print("Failed to initialize I2C. Exiting.")
            return
        
        # Scan I2C bus
        try:
            devices = i2c.scan()
            safe_print(f"I2C devices found: {[hex(device) for device in devices]}")
        except Exception as e:
            safe_print(f"I2C scan error: {e}")
            devices = []
        
        # Check if TCA9548A is found
        if TCA9548A_ADDRESS not in devices:
            safe_print("TCA9548A not found. Check your wiring and power supply.")
            return
        else:
            safe_print("TCA9548A found at address 0x70")
        
        # Initialize sensors array
        connected_sensors = [False] * NUM_SENSORS
        
        # Power on sensors one by one with delays
        safe_print("\n=== Powering on sensors sequentially ===")
        for channel in range(NUM_SENSORS):
            safe_print(f"\n--- Initializing sensor on channel {channel} ---")
            
            if power_on_sensor(i2c, channel):
                connected_sensors[channel] = True
                safe_print(f"✅ Sensor {channel + 1} (Channel {channel}) initialized successfully!")
            else:
                safe_print(f"❌ Failed to initialize sensor {channel + 1} (Channel {channel})")
            
            # Delay between sensor initializations to reduce power surge
            time.sleep_ms(1000)
            
            # Garbage collection to free memory
            gc.collect()
        
        # Display connection status
        safe_print("\n=== Final sensor connection status ===")
        for i, connected in enumerate(connected_sensors):
            status = "Connected" if connected else "Not Connected"
            safe_print(f"Sensor {i+1} (Channel {i}) status: {status}")
        
        num_connected = sum(connected_sensors)
        safe_print(f"\nTotal connected sensors: {num_connected}")
        
        if num_connected == 0:
            safe_print("No sensors connected. Check power supply and wiring.")
            return
        
        # Start data output
        safe_print(f"\nReading Z-axis acceleration data at {SAMPLE_RATE_MS}ms intervals (Ctrl+C to stop)...")
        safe_print("sensor1,sensor2,sensor3,sensor4,sensor5,sensor6,sensor7,sensor8")
        
        last_read_time = time.ticks_ms()
        
        try:
            while True:
                current_time = time.ticks_ms()
                if time.ticks_diff(current_time, last_read_time) >= SAMPLE_RATE_MS:
                    last_read_time = current_time
                    
                    values = []
                    for channel in range(NUM_SENSORS):
                        if connected_sensors[channel]:
                            z_g = read_accel_z(i2c, channel)
                        else:
                            z_g = 0.0
                        values.append(f"{z_g:.3f}")
                    
                    try:
                        print(f"{','.join(values)}")
                    except:
                        pass
                    
                    # Less frequent garbage collection to reduce power
                    if current_time % 5000 < SAMPLE_RATE_MS:
                        gc.collect()
                else:
                    time.sleep_ms(2)  # Slightly longer sleep to save power
        except KeyboardInterrupt:
            safe_print("Stopped by user")
        except Exception as e:
            safe_print(f"Runtime error: {e}")
                
    except Exception as e:
        safe_print(f"Main function error: {e}")
    
    safe_print("Program ended")

if __name__ == "__main__":
    main()
