#!/usr/bin/env python3
"""
Monitor power management sensor detection
"""
import serial
import time

def monitor_power_management():
    try:
        print("전원 관리 센서 감지 모니터링...")
        print("전원 사이클링으로 불안정한 센서들을 감지합니다...")
        ser = serial.Serial("COM14", 115200, timeout=25)
        time.sleep(3)
        
        print("전원 관리 과정 모니터링 중...")
        start_time = time.time()
        messages = []
        detected_sensors = set()
        cycle_results = {}
        
        while time.time() - start_time < 300:  # 5분간 모니터링
            if ser.in_waiting > 0:
                try:
                    line = ser.readline().decode('utf-8', errors='ignore').strip()
                    if line:
                        messages.append(line)
                        print(f"  {line}")
                        
                        # 중요한 이벤트 추적
                        if "Power Management Sensor Program" in line:
                            print("🔋 전원 관리 센서 프로그램 시작!")
                            
                        elif "CPU frequency reduced" in line:
                            print("⚡ CPU 주파수 80MHz로 절전 모드")
                            
                        elif "Power Cycling Sensor Detection" in line:
                            print("🔄 전원 사이클링 감지 시작!")
                            
                        elif "Scan cycle" in line:
                            cycle_num = line.split("cycle")[1].split("/")[0].strip()
                            print(f"🔍 스캔 사이클 {cycle_num}")
                            
                        elif "All channels disabled - sensors powered down" in line:
                            print("🔌 모든 센서 전원 차단")
                            
                        elif "All channels enabled - sensors powered up" in line:
                            print("⚡ 모든 센서 전원 공급")
                            
                        elif "MPU6050 detected" in line and "Cycle" in line:
                            try:
                                parts = line.split()
                                cycle_num = None
                                channel_num = None
                                for i, part in enumerate(parts):
                                    if part == "Cycle" and i + 1 < len(parts):
                                        cycle_num = parts[i + 1].replace(":", "")
                                    elif part == "Channel" and i + 1 < len(parts):
                                        channel_num = int(parts[i + 1])
                                
                                if cycle_num and channel_num is not None:
                                    detected_sensors.add(channel_num)
                                    if cycle_num not in cycle_results:
                                        cycle_results[cycle_num] = []
                                    cycle_results[cycle_num].append(channel_num)
                                    print(f"✅ 사이클 {cycle_num}에서 채널 {channel_num} 감지!")
                            except:
                                pass
                        
                        elif "All detected sensors across cycles:" in line:
                            sensors_str = line.split(":")[-1].strip()
                            print(f"🎯 전체 감지된 센서: {sensors_str}")
                            
                        elif "Sensors detected with power cycling:" in line:
                            count = line.split(":")[-1].strip()
                            print(f"🔋 전원 사이클링으로 감지된 센서: {count}개")
                            
                        elif "Total sensors initialized:" in line:
                            total = line.split(":")[-1].strip()
                            print(f"🎯 최종 초기화된 센서: {total}개")
                            
                        elif "sensor1,sensor2,sensor3" in line:
                            print("📊 데이터 출력 시작!")
                            
                        elif ',' in line and line.count(',') == 7:
                            try:
                                values = line.split(',')
                                if len(values) == 8:
                                    non_zero_count = sum(1 for v in values if float(v) != 0.0)
                                    if non_zero_count > 0:
                                        active_sensors = []
                                        for i, v in enumerate(values):
                                            if float(v) != 0.0:
                                                active_sensors.append(f"센서{i+1}")
                                        print(f"📈 활성 센서: {', '.join(active_sensors)} ({non_zero_count}개)")
                                        
                                        if non_zero_count > 2:
                                            print(f"🎉 전원 관리로 더 많은 센서 감지! (이전: 2개 → 현재: {non_zero_count}개)")
                                        
                                        # 몇 줄 더 읽고 종료
                                        for _ in range(3):
                                            if ser.in_waiting > 0:
                                                line = ser.readline().decode('utf-8', errors='ignore').strip()
                                                if line and ',' in line:
                                                    print(f"  {line}")
                                            time.sleep(0.1)
                                        break
                            except:
                                pass
                        
                except:
                    pass
            time.sleep(0.1)
        
        print(f"\n=== 전원 관리 결과 요약 ===")
        print(f"총 메시지: {len(messages)}개")
        print(f"감지된 센서 채널: {sorted(detected_sensors)}")
        print(f"사이클별 결과: {cycle_results}")
        
        if len(detected_sensors) > 2:
            print(f"🎉 전원 관리 성공! {len(detected_sensors)}개 센서 감지 (이전: 2개)")
        elif len(detected_sensors) == 2:
            print("📊 이전과 동일한 센서 개수 (2개)")
        else:
            print("⚠️ 센서 감지 개수 감소")
        
        # 불안정한 센서 분석
        unstable_sensors = []
        for sensor in detected_sensors:
            appearances = sum(1 for cycle_sensors in cycle_results.values() if sensor in cycle_sensors)
            if appearances < len(cycle_results):
                unstable_sensors.append(sensor)
        
        if unstable_sensors:
            print(f"⚠️ 불안정한 센서 (전원 문제): {unstable_sensors}")
        
        ser.close()
        
    except Exception as e:
        print(f"오류: {e}")

if __name__ == "__main__":
    monitor_power_management()
