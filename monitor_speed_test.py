#!/usr/bin/env python3
"""
Monitor I2C speed test results
"""
import serial
import time

def monitor_speed_test():
    try:
        print("=== I2C 속도 테스트 모니터링 ===")
        print("8개 센서의 최대 안정 통신 속도를 찾습니다...")
        ser = serial.Serial("COM14", 115200, timeout=25)
        time.sleep(3)
        
        print("속도 테스트 진행 상황 모니터링 중...")
        start_time = time.time()
        messages = []
        test_results = {}
        current_freq = 0
        
        while time.time() - start_time < 600:  # 10분간 모니터링
            if ser.in_waiting > 0:
                try:
                    line = ser.readline().decode('utf-8', errors='ignore').strip()
                    if line:
                        messages.append(line)
                        print(f"  {line}")
                        
                        # 테스트 진행 상황 추적
                        if "I2C Speed Performance Test" in line:
                            print("🚀 I2C 속도 성능 테스트 시작!")
                            
                        elif "Testing" in line and "Hz" in line and "kHz" in line:
                            try:
                                # Extract frequency
                                parts = line.split()
                                for part in parts:
                                    if part.endswith("Hz"):
                                        current_freq = int(part.replace("Hz", ""))
                                        break
                                print(f"🔍 {current_freq}Hz ({current_freq/1000:.0f}kHz) 테스트 시작")
                            except:
                                pass
                        
                        elif "I2C devices:" in line:
                            devices = line.split(":")[-1].strip()
                            print(f"🔍 감지된 I2C 장치: {devices}")
                            
                        elif "Connected sensors:" in line:
                            try:
                                sensor_info = line.split(":")[-1].strip()
                                sensor_count = int(sensor_info.split("/")[0])
                                print(f"📊 연결된 센서: {sensor_count}/8개")
                            except:
                                pass
                        
                        elif "Performance:" in line and "Hz" in line:
                            print(f"📈 성능 결과 발표!")
                            
                        elif "Success rate:" in line:
                            try:
                                rate = float(line.split(":")[-1].replace("%", "").strip())
                                print(f"✅ 성공률: {rate:.1f}%")
                            except:
                                pass
                        
                        elif "EXCELLENT" in line:
                            freq_str = line.split("Hz")[0].split()[-1]
                            try:
                                freq = int(freq_str)
                                test_results[freq] = "EXCELLENT"
                                print(f"🏆 {freq}Hz: 최고 성능!")
                            except:
                                pass
                        
                        elif "GOOD" in line:
                            freq_str = line.split("Hz")[0].split()[-1]
                            try:
                                freq = int(freq_str)
                                test_results[freq] = "GOOD"
                                print(f"✅ {freq}Hz: 우수한 성능!")
                            except:
                                pass
                        
                        elif "FAIR" in line:
                            freq_str = line.split("Hz")[0].split()[-1]
                            try:
                                freq = int(freq_str)
                                test_results[freq] = "FAIR"
                                print(f"⚠️ {freq}Hz: 보통 성능")
                            except:
                                pass
                        
                        elif "POOR" in line:
                            freq_str = line.split("Hz")[0].split()[-1]
                            try:
                                freq = int(freq_str)
                                test_results[freq] = "POOR"
                                print(f"❌ {freq}Hz: 성능 부족")
                            except:
                                pass
                        
                        elif "Maximum stable frequency:" in line:
                            try:
                                freq_part = line.split(":")[-1].strip()
                                max_freq = int(freq_part.split("Hz")[0])
                                print(f"🎯 최대 안정 주파수: {max_freq}Hz ({max_freq/1000:.0f}kHz)")
                            except:
                                pass
                        
                        elif "SPEED TEST SUMMARY" in line:
                            print("📋 최종 결과 요약 시작!")
                        
                        elif "PASS" in line or "FAIL" in line:
                            print(f"📊 {line}")
                        
                        # 센서 데이터 감지 (테스트 중 실제 데이터)
                        elif ',' in line and line.count(',') == 7:
                            try:
                                values = line.split(',')
                                if len(values) == 8:
                                    non_zero_count = sum(1 for v in values if float(v) != 0.0)
                                    if non_zero_count >= 6:  # 대부분의 센서가 작동 중
                                        print(f"📈 실시간 데이터: {non_zero_count}/8 센서 활성")
                            except:
                                pass
                        
                except:
                    pass
            time.sleep(0.1)
        
        print(f"\n=== 속도 테스트 최종 결과 ===")
        print(f"총 메시지: {len(messages)}개")
        print(f"테스트 결과: {test_results}")
        
        if test_results:
            # 최고 성능 주파수 찾기
            excellent_freqs = [freq for freq, result in test_results.items() if result == "EXCELLENT"]
            good_freqs = [freq for freq, result in test_results.items() if result == "GOOD"]
            
            if excellent_freqs:
                max_excellent = max(excellent_freqs)
                print(f"🏆 최고 성능 주파수: {max_excellent}Hz ({max_excellent/1000:.0f}kHz)")
            elif good_freqs:
                max_good = max(good_freqs)
                print(f"✅ 최대 우수 성능: {max_good}Hz ({max_good/1000:.0f}kHz)")
            
            # 권장 설정
            working_freqs = [freq for freq, result in test_results.items() 
                           if result in ["EXCELLENT", "GOOD"]]
            if working_freqs:
                recommended = max(working_freqs)
                print(f"\n💡 권장 설정: {recommended}Hz ({recommended/1000:.0f}kHz)")
                print(f"   이 속도로 센서 프로그램을 실행하세요!")
        else:
            print("⚠️ 테스트 결과를 찾을 수 없습니다.")
        
        ser.close()
        
    except Exception as e:
        print(f"오류: {e}")

if __name__ == "__main__":
    monitor_speed_test()
