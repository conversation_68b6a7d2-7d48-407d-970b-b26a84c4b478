#!/usr/bin/env python3
"""
Script to upload sensor_25ms_no_timestamp.py to NodeMCU via serial connection
"""
import serial
import time
import sys

def upload_file_to_nodemcu(port, filename, target_filename='main.py'):
    """
    Upload a Python file to NodeMCU using raw REPL mode
    """
    try:
        # Open serial connection
        print(f"Connecting to {port}...")
        ser = serial.Serial(port, 115200, timeout=10)
        time.sleep(2)  # Wait for connection to stabilize
        
        # Send Ctrl+C to interrupt any running program
        ser.write(b'\x03')
        time.sleep(0.5)
        
        # Send Ctrl+A to enter raw REPL mode
        ser.write(b'\x01')
        time.sleep(0.5)
        
        # Read response
        response = ser.read_all().decode('utf-8', errors='ignore')
        print("Initial response:", response)
        
        # Check if we're in raw REPL mode
        if 'raw REPL' not in response:
            print("Entering raw REPL mode...")
            ser.write(b'\x01')
            time.sleep(1)
            response = ser.read_all().decode('utf-8', errors='ignore')
            print("Raw REPL response:", response)
        
        # Read the file to upload
        print(f"Reading {filename}...")
        with open(filename, 'r', encoding='utf-8') as f:
            file_content = f.read()
        
        # Create upload command
        upload_command = f"""
with open('{target_filename}', 'w') as f:
    f.write('''{file_content}''')
print('File uploaded successfully')
"""
        
        print(f"Uploading {filename} as {target_filename}...")
        
        # Send the upload command
        ser.write(upload_command.encode('utf-8'))
        ser.write(b'\x04')  # Send Ctrl+D to execute
        
        # Wait for execution and read response
        time.sleep(3)
        response = ser.read_all().decode('utf-8', errors='ignore')
        print("Upload response:", response)
        
        # Exit raw REPL mode
        ser.write(b'\x02')  # Ctrl+B to exit raw REPL
        time.sleep(1)
        
        # List files to verify upload
        print("Verifying upload...")
        ser.write(b'import os; print(os.listdir())\r\n')
        time.sleep(1)
        response = ser.read_all().decode('utf-8', errors='ignore')
        print("File list:", response)
        
        ser.close()
        print(f"Successfully uploaded {filename} to NodeMCU as {target_filename}")
        return True
        
    except Exception as e:
        print(f"Error uploading file: {e}")
        return False

if __name__ == "__main__":
    port = "COM14"
    filename = "sensor_25ms_no_timestamp.py"
    
    if upload_file_to_nodemcu(port, filename):
        print("Upload completed successfully!")
    else:
        print("Upload failed!")
