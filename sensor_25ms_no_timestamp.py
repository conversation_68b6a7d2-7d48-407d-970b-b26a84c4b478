"""
<PERSON><PERSON><PERSON> to read Z-axis data from up to 8 MPU6050 sensors through TCA9548A multiplexer
Displays data for all 8 sensors, showing 0 for unconnected sensors
Optimized for 25ms sampling rate with improved sensor detection
Modified to remove timestamp for better graph compatibility
"""
import time
from machine import I2C, Pin, freq

# Configuration
I2C_SCL_PIN = 5  # D1 on NodeMCU
I2C_SDA_PIN = 4  # D2 on NodeMCU
TCA9548A_ADDRESS = 0x70  # Default address of TCA9548A
MPU6050_ADDRESS = 0x68   # Default address of MPU6050
SAMPLE_RATE_MS = 25      # How often to read sensor data (in milliseconds)
NUM_SENSORS = 8          # Total number of sensors to support
SCAN_DELAY_MS = 100      # Delay for sensor scanning (in milliseconds)
INIT_DELAY_MS = 50       # Delay after sensor initialization (in milliseconds)
CHANNEL_SWITCH_DELAY_MS = 5  # Delay after channel switching (in milliseconds)

# Try to increase CPU frequency for better performance
try:
    freq(160000000)  # Set to 160MHz for faster processing
    print("CPU frequency set to 160MHz")
except:
    print("Could not set CPU frequency")

# Initialize I2C with higher frequency for faster communication
i2c = I2C(scl=Pin(I2C_SCL_PIN), sda=Pin(I2C_SDA_PIN), freq=400000)

# Function to select TCA9548A channel with minimal delay for faster reading
def select_channel(channel):
    if not 0 <= channel <= 7:
        raise ValueError("Channel must be between 0 and 7")
    i2c.writeto(TCA9548A_ADDRESS, bytes([1 << channel]))
    time.sleep_ms(CHANNEL_SWITCH_DELAY_MS)  # Minimal delay for faster reading

# Function to read Z-axis acceleration from MPU6050 (optimized for speed)
def read_accel_z(channel):
    try:
        # Select the channel
        select_channel(channel)
        
        # Read Z-axis acceleration registers directly
        data = i2c.readfrom_mem(MPU6050_ADDRESS, 0x3F, 2)
        
        # Convert to 16-bit signed value
        z = (data[0] << 8) | data[1]
        if z > 32767:
            z -= 65536
        
        # Convert to g (±2g range)
        z_g = z / 16384.0
        
        return z_g
    except Exception as e:
        # Return 0 if sensor is not connected or error occurs
        return 0.0

# Function to initialize MPU6050 sensor
def initialize_mpu6050(channel):
    try:
        select_channel(channel)
        
        # Wake up the MPU6050 (in case it's in sleep mode)
        i2c.writeto_mem(MPU6050_ADDRESS, 0x6B, b'\x00')
        time.sleep_ms(INIT_DELAY_MS)
        
        # Set accelerometer range to ±2g
        i2c.writeto_mem(MPU6050_ADDRESS, 0x1C, b'\x00')
        time.sleep_ms(INIT_DELAY_MS)
        
        # Set sample rate divider for faster sampling
        i2c.writeto_mem(MPU6050_ADDRESS, 0x19, b'\x00')  # Set sample rate divider to 0
        
        # Set digital low pass filter to minimum
        i2c.writeto_mem(MPU6050_ADDRESS, 0x1A, b'\x01')  # Set DLPF to 1 (188Hz bandwidth)
        
        # Verify that the sensor is responding
        who_am_i = i2c.readfrom_mem(MPU6050_ADDRESS, 0x75, 1)
        if who_am_i[0] != 0x68:  # MPU6050 should return 0x68 for WHO_AM_I register
            print(f"Warning: Unexpected WHO_AM_I value on channel {channel}: {hex(who_am_i[0])}")
            return False
        
        return True
    except Exception as e:
        print(f"Error initializing sensor on channel {channel}: {e}")
        return False

# Scan I2C bus
devices = i2c.scan()
print("I2C devices found:", [hex(device) for device in devices])

# Check if TCA9548A is found
if TCA9548A_ADDRESS not in devices:
    print("TCA9548A not found. Check your wiring.")
else:
    print("TCA9548A found at address 0x70")
    
    # Scan each channel for MPU6050 sensors
    connected_sensors = [False] * NUM_SENSORS
    
    print("Scanning for sensors...")
    for channel in range(NUM_SENSORS):
        select_channel(channel)
        time.sleep_ms(SCAN_DELAY_MS)
        
        channel_devices = i2c.scan()
        print(f"Channel {channel} devices: {[hex(device) for device in channel_devices]}")
        
        if MPU6050_ADDRESS in channel_devices:
            print(f"MPU6050 found on channel {channel}, initializing...")
            if initialize_mpu6050(channel):
                connected_sensors[channel] = True
                print(f"MPU6050 on channel {channel} initialized successfully")
            else:
                print(f"Failed to initialize MPU6050 on channel {channel}")
    
    # Display connection status for all sensors
    print("\nSensor connection status:")
    for i, connected in enumerate(connected_sensors):
        status = "Connected" if connected else "Not Connected"
        print(f"Sensor {i+1} status: {status}")
    
    # Count connected sensors
    num_connected = sum(connected_sensors)
    print(f"\nTotal connected sensors: {num_connected}")
    
    if num_connected == 0:
        print("No sensors connected. Check your wiring and try again.")
    else:
        # Continuously read and display Z-axis data
        print(f"\nReading Z-axis acceleration data at {SAMPLE_RATE_MS}ms intervals (Ctrl+C to stop)...")
        # Modified: Removed timestamp from header
        print("sensor1,sensor2,sensor3,sensor4,sensor5,sensor6,sensor7,sensor8")
        
        # For timing precision
        last_read_time = time.ticks_ms()
        start_time = last_read_time
        
        try:
            while True:
                current_time = time.ticks_ms()
                # Only read if it's time to do so (every 25ms)
                if time.ticks_diff(current_time, last_read_time) >= SAMPLE_RATE_MS:
                    elapsed_ms = time.ticks_diff(current_time, start_time)
                    last_read_time = current_time
                    
                    # Read Z-axis data from all sensors
                    values = []
                    for channel in range(NUM_SENSORS):
                        if connected_sensors[channel]:
                            # Read data from connected sensor
                            z_g = read_accel_z(channel)
                        else:
                            # Use 0 for unconnected sensor
                            z_g = 0.0
                        
                        values.append(f"{z_g:.3f}")
                    
                    # Modified: Print only sensor values without timestamp
                    print(f"{','.join(values)}")
                else:
                    # Yield to other tasks for a very short time
                    time.sleep_ms(1)
        except KeyboardInterrupt:
            print("Stopped by user")
