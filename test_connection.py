#!/usr/bin/env python3
"""
Test NodeMCU connection
"""
import serial
import time

def test_connection(port):
    try:
        print(f"Testing connection to {port}...")
        ser = serial.Serial(port, 115200, timeout=5)
        time.sleep(2)
        
        # Send a simple command
        ser.write(b'\r\n')
        time.sleep(1)
        
        # Read response
        response = ser.read_all().decode('utf-8', errors='ignore')
        print(f"Response: {repr(response)}")
        
        # Send a Python command
        ser.write(b'print("Hello from NodeMCU")\r\n')
        time.sleep(1)
        
        response = ser.read_all().decode('utf-8', errors='ignore')
        print(f"Python response: {repr(response)}")
        
        ser.close()
        return True
        
    except Exception as e:
        print(f"Connection error: {e}")
        return False

if __name__ == "__main__":
    test_connection("COM14")
