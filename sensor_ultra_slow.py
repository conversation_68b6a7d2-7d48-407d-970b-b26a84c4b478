"""
Ultra-slow initialization method for maximum sensor detection
Uses very slow I2C speed and long delays for stability
"""
import time
from machine import I2C, Pin, freq
import gc

# Configuration - Ultra conservative settings
I2C_SCL_PIN = 5  # D1 on NodeMCU
I2C_SDA_PIN = 4  # D2 on NodeMCU
TCA9548A_ADDRESS = 0x70
MPU6050_ADDRESS = 0x68
SAMPLE_RATE_MS = 25
NUM_SENSORS = 8

# Ultra slow timing settings
ULTRA_SLOW_I2C_FREQ = 10000      # 10kHz - very slow
CHANNEL_SWITCH_DELAY = 2000      # 2 seconds
INIT_DELAY = 3000                # 3 seconds
SCAN_DELAY = 5000                # 5 seconds
POWER_SETTLE_DELAY = 10000       # 10 seconds

def safe_print(msg):
    try:
        print(msg)
    except:
        pass

def ultra_slow_setup():
    """Ultra slow setup with maximum stability"""
    try:
        # Set very low CPU frequency to reduce power consumption
        freq(80000000)
        safe_print("CPU frequency set to 80MHz for power saving")
        
        # Initialize I2C at ultra-low frequency
        safe_print(f"Initializing I2C at ultra-slow {ULTRA_SLOW_I2C_FREQ}Hz...")
        i2c = I2C(scl=Pin(I2C_SCL_PIN), sda=Pin(I2C_SDA_PIN), freq=ULTRA_SLOW_I2C_FREQ)
        
        # Very long stabilization delay
        safe_print("Waiting for I2C to stabilize...")
        time.sleep_ms(5000)
        
        # Test I2C
        devices = i2c.scan()
        safe_print(f"I2C devices found: {[hex(d) for d in devices]}")
        
        return i2c
    except Exception as e:
        safe_print(f"Setup error: {e}")
        return None

def ultra_slow_channel_select(i2c, channel):
    """Ultra slow channel selection with maximum delays"""
    try:
        safe_print(f"Selecting channel {channel} (ultra slow)...")
        
        # Multiple attempts with very long delays
        for attempt in range(10):  # More attempts
            try:
                # First, disable all channels
                i2c.writeto(TCA9548A_ADDRESS, b'\x00')
                time.sleep_ms(1000)  # 1 second delay
                
                # Then select the target channel
                channel_byte = 1 << channel
                i2c.writeto(TCA9548A_ADDRESS, bytes([channel_byte]))
                
                # Very long delay for channel switching
                safe_print(f"Waiting {CHANNEL_SWITCH_DELAY}ms for channel {channel} to stabilize...")
                time.sleep_ms(CHANNEL_SWITCH_DELAY)
                
                # Verify channel selection by scanning
                devices = i2c.scan()
                safe_print(f"Channel {channel} scan result: {[hex(d) for d in devices]}")
                
                return True
                
            except Exception as e:
                safe_print(f"Channel {channel} selection attempt {attempt + 1} failed: {e}")
                time.sleep_ms(1000)  # Wait before retry
        
        safe_print(f"Failed to select channel {channel} after all attempts")
        return False
        
    except Exception as e:
        safe_print(f"Channel selection error: {e}")
        return False

def ultra_slow_mpu6050_init(i2c, channel):
    """Ultra slow MPU6050 initialization"""
    try:
        safe_print(f"Starting ultra-slow initialization for channel {channel}...")
        
        # Step 1: Select channel with ultra slow method
        if not ultra_slow_channel_select(i2c, channel):
            return False
        
        # Step 2: Check if sensor is present
        devices = i2c.scan()
        if MPU6050_ADDRESS not in devices:
            safe_print(f"No MPU6050 found on channel {channel}")
            return False
        
        safe_print(f"MPU6050 detected on channel {channel}, starting initialization...")
        
        # Step 3: Device reset with ultra long delays
        safe_print(f"Step 1: Device reset for channel {channel}")
        for reset_attempt in range(5):
            try:
                i2c.writeto_mem(MPU6050_ADDRESS, 0x6B, b'\x80')  # Device reset
                safe_print(f"Reset command sent, waiting {INIT_DELAY}ms...")
                time.sleep_ms(INIT_DELAY)
                break
            except Exception as e:
                safe_print(f"Reset attempt {reset_attempt + 1} failed: {e}")
                time.sleep_ms(2000)
        
        # Step 4: Wake up with ultra long delays
        safe_print(f"Step 2: Wake up for channel {channel}")
        for wake_attempt in range(10):  # More attempts
            try:
                i2c.writeto_mem(MPU6050_ADDRESS, 0x6B, b'\x00')  # Wake up
                safe_print(f"Wake up command sent, waiting {INIT_DELAY}ms...")
                time.sleep_ms(INIT_DELAY)
                break
            except Exception as e:
                safe_print(f"Wake attempt {wake_attempt + 1} failed: {e}")
                time.sleep_ms(1000)
        else:
            safe_print(f"Failed to wake up sensor on channel {channel}")
            return False
        
        # Step 5: Configure accelerometer with ultra long delays
        safe_print(f"Step 3: Configure accelerometer for channel {channel}")
        try:
            i2c.writeto_mem(MPU6050_ADDRESS, 0x1C, b'\x00')  # ±2g range
            safe_print(f"Accelerometer configured, waiting {INIT_DELAY}ms...")
            time.sleep_ms(INIT_DELAY)
        except Exception as e:
            safe_print(f"Accelerometer config failed: {e}")
        
        # Step 6: Set sample rate with ultra long delays
        safe_print(f"Step 4: Set sample rate for channel {channel}")
        try:
            i2c.writeto_mem(MPU6050_ADDRESS, 0x19, b'\x07')  # Slower sample rate
            safe_print(f"Sample rate set, waiting {INIT_DELAY}ms...")
            time.sleep_ms(INIT_DELAY)
        except Exception as e:
            safe_print(f"Sample rate config failed: {e}")
        
        # Step 7: Set DLPF with ultra long delays
        safe_print(f"Step 5: Set DLPF for channel {channel}")
        try:
            i2c.writeto_mem(MPU6050_ADDRESS, 0x1A, b'\x06')  # Lowest bandwidth
            safe_print(f"DLPF set, waiting {INIT_DELAY}ms...")
            time.sleep_ms(INIT_DELAY)
        except Exception as e:
            safe_print(f"DLPF config failed: {e}")
        
        # Step 8: Verify WHO_AM_I (optional)
        safe_print(f"Step 6: Verify WHO_AM_I for channel {channel}")
        try:
            who_am_i = i2c.readfrom_mem(MPU6050_ADDRESS, 0x75, 1)
            safe_print(f"Channel {channel} WHO_AM_I: {hex(who_am_i[0])}")
        except Exception as e:
            safe_print(f"WHO_AM_I read failed: {e}")
        
        # Step 9: Test accelerometer read
        safe_print(f"Step 7: Test accelerometer read for channel {channel}")
        try:
            accel_data = i2c.readfrom_mem(MPU6050_ADDRESS, 0x3B, 6)
            safe_print(f"Channel {channel} accelerometer test read successful")
        except Exception as e:
            safe_print(f"Accelerometer test read failed: {e}")
        
        safe_print(f"✅ Channel {channel} ultra-slow initialization completed!")
        return True
        
    except Exception as e:
        safe_print(f"Ultra-slow init failed for channel {channel}: {e}")
        return False

def read_accel_z_ultra_slow(i2c, channel):
    """Ultra slow accelerometer reading"""
    try:
        # Select channel with delay
        if not ultra_slow_channel_select(i2c, channel):
            return 0.0
        
        # Read with delay
        data = i2c.readfrom_mem(MPU6050_ADDRESS, 0x3F, 2)
        z = (data[0] << 8) | data[1]
        if z > 32767:
            z -= 65536
        z_g = z / 16384.0
        return z_g
    except:
        return 0.0

def main():
    """Main function with ultra-slow initialization"""
    try:
        safe_print("=== ULTRA-SLOW INITIALIZATION METHOD ===")
        safe_print("This will take a very long time but should detect more sensors")
        
        # Ultra slow setup
        i2c = ultra_slow_setup()
        if i2c is None:
            safe_print("Failed to initialize I2C")
            return
        
        # Scan I2C bus
        devices = i2c.scan()
        safe_print(f"Initial I2C scan: {[hex(d) for d in devices]}")
        
        if TCA9548A_ADDRESS not in devices:
            safe_print("TCA9548A not found")
            return
        
        safe_print("TCA9548A found, starting ultra-slow sensor detection...")
        
        # Initialize sensors array
        connected_sensors = [False] * NUM_SENSORS
        
        # Ultra slow sensor initialization
        safe_print("\n=== ULTRA-SLOW SENSOR INITIALIZATION ===")
        for channel in range(NUM_SENSORS):
            safe_print(f"\n{'='*50}")
            safe_print(f"INITIALIZING SENSOR {channel + 1} ON CHANNEL {channel}")
            safe_print(f"{'='*50}")
            
            if ultra_slow_mpu6050_init(i2c, channel):
                connected_sensors[channel] = True
                safe_print(f"✅ SENSOR {channel + 1} SUCCESSFULLY INITIALIZED!")
            else:
                safe_print(f"❌ SENSOR {channel + 1} INITIALIZATION FAILED")
            
            # Very long delay between sensors
            safe_print(f"Waiting {POWER_SETTLE_DELAY}ms before next sensor...")
            time.sleep_ms(POWER_SETTLE_DELAY)
            gc.collect()
        
        # Final status
        total_connected = sum(connected_sensors)
        safe_print(f"\n{'='*50}")
        safe_print(f"ULTRA-SLOW INITIALIZATION COMPLETE")
        safe_print(f"{'='*50}")
        safe_print(f"Total sensors detected: {total_connected}")
        
        for i, connected in enumerate(connected_sensors):
            status = "✅ CONNECTED" if connected else "❌ NOT CONNECTED"
            safe_print(f"Sensor {i+1} (Channel {i}): {status}")
        
        if total_connected == 0:
            safe_print("No sensors detected. Check hardware connections.")
            return
        
        # Start data output
        safe_print(f"\nStarting data output...")
        safe_print("sensor1,sensor2,sensor3,sensor4,sensor5,sensor6,sensor7,sensor8")
        
        last_read_time = time.ticks_ms()
        
        while True:
            current_time = time.ticks_ms()
            if time.ticks_diff(current_time, last_read_time) >= SAMPLE_RATE_MS:
                last_read_time = current_time
                
                values = []
                for channel in range(NUM_SENSORS):
                    if connected_sensors[channel]:
                        z_g = read_accel_z_ultra_slow(i2c, channel)
                    else:
                        z_g = 0.0
                    values.append(f"{z_g:.3f}")
                
                print(f"{','.join(values)}")
                
                if current_time % 30000 < SAMPLE_RATE_MS:  # Less frequent GC
                    gc.collect()
            else:
                time.sleep_ms(5)  # Longer sleep
                
    except KeyboardInterrupt:
        safe_print("Stopped by user")
    except Exception as e:
        safe_print(f"Main error: {e}")

if __name__ == "__main__":
    main()
