# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['C:\\NEW2\\MPU6050_25ms_Backup\\drum_sensor_10ms_highspeed.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=['pyqtgraph', 'pyqtgraph.Qt', 'pyqtgraph.Qt.QtCore', 'pyqtgraph.Qt.QtGui', 'pyqtgraph.Qt.QtWidgets', 'numpy', 'serial', 'serial.tools.list_ports', 'pyautogui', 'json', 'queue', 'threading', 'OpenGL', 'OpenGL.GL', 'OpenGL.arrays', 'PyQt5', 'PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'PyQt5.QtOpenGL'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='DrumSensor_10ms_HighSpeed_v2',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
