#!/usr/bin/env python3
"""
Test sensor on each TCA9548A channel
"""
import serial
import time

def test_sensor_channels(port):
    try:
        print("각 채널별 센서 테스트...")
        ser = serial.Serial(port, 115200, timeout=10)
        time.sleep(2)
        
        # 프로그램 중단
        for i in range(3):
            ser.write(b'\x03')
            time.sleep(0.5)
        
        ser.read_all()
        
        # I2C 초기화
        commands = [
            "from machine import I2C, Pin",
            "i2c = I2C(scl=Pin(5), sda=Pin(4), freq=100000)",
            "print('I2C initialized')"
        ]
        
        for cmd in commands:
            ser.write(f"{cmd}\r\n".encode('utf-8'))
            time.sleep(1)
            response = ser.read_all().decode('utf-8', errors='ignore')
            print(f"초기화: {repr(response)}")
        
        # 각 채널 테스트
        print("\n=== 각 채널별 센서 테스트 ===")
        for channel in range(8):
            print(f"\n--- 채널 {channel} 테스트 ---")
            
            test_commands = [
                f"# 채널 {channel} 선택",
                f"try:",
                f"    i2c.writeto(0x70, bytes([{1 << channel}]))",
                f"    import time",
                f"    time.sleep_ms(10)",
                f"    devices = i2c.scan()",
                f"    print('Channel {channel} devices:', [hex(d) for d in devices])",
                f"    if 0x68 in devices:",
                f"        who_am_i = i2c.readfrom_mem(0x68, 0x75, 1)",
                f"        print('Channel {channel} MPU6050 WHO_AM_I:', hex(who_am_i[0]))",
                f"        # Z축 데이터 읽기 테스트",
                f"        z_data = i2c.readfrom_mem(0x68, 0x3F, 2)",
                f"        z_raw = (z_data[0] << 8) | z_data[1]",
                f"        if z_raw > 32767: z_raw -= 65536",
                f"        z_g = z_raw / 16384.0",
                f"        print('Channel {channel} Z-axis:', z_g)",
                f"except Exception as e:",
                f"    print('Channel {channel} error:', e)"
            ]
            
            for cmd in test_commands:
                if cmd.startswith('#'):
                    continue
                ser.write(f"{cmd}\r\n".encode('utf-8'))
                time.sleep(0.5)
            
            # 결과 읽기
            time.sleep(2)
            response = ser.read_all().decode('utf-8', errors='ignore')
            if response.strip():
                lines = response.split('\n')
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('>>>') and not line.startswith('...'):
                        print(f"  {line}")
        
        # 직접 연결된 센서 테스트 (TCA9548A 없이)
        print("\n=== 직접 연결 센서 테스트 ===")
        direct_commands = [
            "# TCA9548A 없이 직접 센서 테스트",
            "try:",
            "    devices = i2c.scan()",
            "    print('Direct scan devices:', [hex(d) for d in devices])",
            "    if 0x68 in devices:",
            "        who_am_i = i2c.readfrom_mem(0x68, 0x75, 1)",
            "        print('Direct MPU6050 WHO_AM_I:', hex(who_am_i[0]))",
            "        # 센서 초기화",
            "        i2c.writeto_mem(0x68, 0x6B, b'\\x00')  # Wake up",
            "        time.sleep_ms(50)",
            "        i2c.writeto_mem(0x68, 0x1C, b'\\x00')  # ±2g range",
            "        time.sleep_ms(50)",
            "        print('Direct sensor initialized')",
            "        # 연속 데이터 읽기",
            "        for i in range(5):",
            "            z_data = i2c.readfrom_mem(0x68, 0x3F, 2)",
            "            z_raw = (z_data[0] << 8) | z_data[1]",
            "            if z_raw > 32767: z_raw -= 65536",
            "            z_g = z_raw / 16384.0",
            "            print(f'Direct reading {i+1}: {z_g:.3f}g')",
            "            time.sleep_ms(100)",
            "except Exception as e:",
            "    print('Direct test error:', e)"
        ]
        
        for cmd in direct_commands:
            if cmd.startswith('#'):
                continue
            ser.write(f"{cmd}\r\n".encode('utf-8'))
            time.sleep(0.2)
        
        # 결과 읽기
        time.sleep(3)
        response = ser.read_all().decode('utf-8', errors='ignore')
        if response.strip():
            lines = response.split('\n')
            for line in lines:
                line = line.strip()
                if line and not line.startswith('>>>') and not line.startswith('...'):
                    print(f"  {line}")
        
        ser.close()
        
    except Exception as e:
        print(f"테스트 오류: {e}")

if __name__ == "__main__":
    test_sensor_channels("COM14")
