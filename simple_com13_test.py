#!/usr/bin/env python3
"""
Simple COM13 connection test
"""
import serial
import time

def simple_test():
    try:
        print("COM13 간단 연결 테스트...")
        ser = serial.Serial("COM13", 115200, timeout=5)
        time.sleep(2)
        
        # 인터럽트 시도
        print("프로그램 중단 시도...")
        for i in range(3):
            ser.write(b'\x03')  # Ctrl+C
            time.sleep(0.5)
        
        # 버퍼 클리어
        ser.read_all()
        
        # 간단한 명령 전송
        print("Python 명령 테스트...")
        ser.write(b'print("Hello from NodeMCU")\r\n')
        time.sleep(2)
        
        response = ser.read_all().decode('utf-8', errors='ignore')
        print(f"응답: {repr(response)}")
        
        if "Hello from NodeMCU" in response:
            print("✅ NodeMCU 연결 성공!")
            
            # 센서 프로그램 수동 실행
            print("센서 프로그램 수동 실행...")
            ser.write(b'exec(open("main.py").read())\r\n')
            time.sleep(3)
            
            # 결과 읽기
            response = ser.read_all().decode('utf-8', errors='ignore')
            print(f"센서 프로그램 응답: {repr(response)}")
            
        else:
            print("❌ NodeMCU 연결 실패")
        
        ser.close()
        
    except Exception as e:
        print(f"오류: {e}")

if __name__ == "__main__":
    simple_test()
