"""
High-speed 10ms sensor program for 8 MPU6050 sensors
Optimized for maximum data rate with 8 sensors
"""
import time
from machine import I2C, Pin, freq
import gc

# Configuration - High speed settings
I2C_SCL_PIN = 5  # D1 on NodeMCU
I2C_SDA_PIN = 4  # D2 on NodeMCU
TCA9548A_ADDRESS = 0x70
MPU6050_ADDRESS = 0x68
SAMPLE_RATE_MS = 10          # 10ms = 100Hz sampling rate
NUM_SENSORS = 8

# High-speed optimized timing
I2C_FREQUENCY = 400000       # 400kHz for high speed
CHANNEL_SWITCH_DELAY = 2     # Minimal delay for speed
INIT_DELAY = 50             # Reduced initialization delay
SCAN_DELAY = 100            # Reduced scan delay

def safe_print(msg):
    try:
        print(msg)
    except:
        pass

def high_speed_setup():
    """Setup optimized for high-speed operation"""
    try:
        # Maximum CPU frequency for high performance
        freq(160000000)  # 160MHz for maximum speed
        safe_print("CPU frequency set to 160MHz for high-speed operation")
        
        # Initialize I2C at high frequency
        i2c = I2C(scl=Pin(I2C_SCL_PIN), sda=Pin(I2C_SDA_PIN), freq=I2C_FREQUENCY)
        safe_print(f"I2C initialized at {I2C_FREQUENCY}Hz for high-speed communication")
        
        # Minimal stabilization delay
        time.sleep_ms(200)
        
        # Test scan
        devices = i2c.scan()
        safe_print(f"I2C devices found: {[hex(d) for d in devices]}")
        
        return i2c
    except Exception as e:
        safe_print(f"Setup error: {e}")
        return None

def fast_channel_select(i2c, channel):
    """Fast channel selection with minimal delay"""
    try:
        i2c.writeto(TCA9548A_ADDRESS, bytes([1 << channel]))
        time.sleep_ms(CHANNEL_SWITCH_DELAY)  # Minimal delay
        return True
    except:
        return False

def fast_mpu6050_init(i2c, channel):
    """Fast MPU6050 initialization"""
    try:
        safe_print(f"Fast init for channel {channel}...")
        
        # Select channel
        if not fast_channel_select(i2c, channel):
            return False
        
        # Check if sensor is present
        devices = i2c.scan()
        if MPU6050_ADDRESS not in devices:
            safe_print(f"No MPU6050 on channel {channel}")
            return False
        
        safe_print(f"MPU6050 detected on channel {channel}")
        
        # Fast initialization sequence
        try:
            # Wake up
            i2c.writeto_mem(MPU6050_ADDRESS, 0x6B, b'\x00')
            time.sleep_ms(INIT_DELAY)
            
            # Set accelerometer range to ±2g
            i2c.writeto_mem(MPU6050_ADDRESS, 0x1C, b'\x00')
            time.sleep_ms(20)
            
            # Set high sample rate for fast operation
            i2c.writeto_mem(MPU6050_ADDRESS, 0x19, b'\x00')  # Highest sample rate
            time.sleep_ms(20)
            
            # Set DLPF for high bandwidth
            i2c.writeto_mem(MPU6050_ADDRESS, 0x1A, b'\x00')  # No filter for speed
            time.sleep_ms(20)
            
        except Exception as e:
            safe_print(f"Configuration failed: {e}")
        
        # Quick verification
        try:
            data = i2c.readfrom_mem(MPU6050_ADDRESS, 0x3F, 2)
            safe_print(f"Channel {channel} test read successful")
        except Exception as e:
            safe_print(f"Test read failed: {e}")
            return False
        
        safe_print(f"✅ Channel {channel} fast init completed!")
        return True
        
    except Exception as e:
        safe_print(f"Fast init failed for channel {channel}: {e}")
        return False

def read_accel_z_fast(i2c, channel):
    """High-speed accelerometer reading"""
    try:
        # Fast channel selection
        i2c.writeto(TCA9548A_ADDRESS, bytes([1 << channel]))
        # No delay for maximum speed
        
        # Read Z-axis data
        data = i2c.readfrom_mem(MPU6050_ADDRESS, 0x3F, 2)
        z = (data[0] << 8) | data[1]
        if z > 32767:
            z -= 65536
        z_g = z / 16384.0
        return z_g
    except:
        return 0.0

def main():
    """Main function optimized for 10ms sampling"""
    try:
        safe_print("=== 10ms High-Speed Sensor Program ===")
        safe_print("100Hz sampling rate with 8 sensors")
        
        # High-speed setup
        i2c = high_speed_setup()
        if i2c is None:
            safe_print("Failed to initialize I2C")
            return
        
        # Check TCA9548A
        devices = i2c.scan()
        if TCA9548A_ADDRESS not in devices:
            safe_print("TCA9548A not found")
            return
        
        safe_print("TCA9548A found, starting high-speed sensor initialization...")
        
        # Initialize sensors
        connected_sensors = [False] * NUM_SENSORS
        
        safe_print("\n=== High-Speed Sensor Initialization ===")
        for channel in range(NUM_SENSORS):
            safe_print(f"--- Channel {channel} ---")
            
            if fast_mpu6050_init(i2c, channel):
                connected_sensors[channel] = True
                safe_print(f"✅ Sensor {channel + 1} ready for high-speed!")
            else:
                safe_print(f"❌ Sensor {channel + 1} failed!")
            
            # Minimal delay between sensors
            time.sleep_ms(500)
        
        # Status report
        total_connected = sum(connected_sensors)
        safe_print(f"\n=== High-Speed Setup Complete ===")
        safe_print(f"Total sensors ready: {total_connected}/8")
        
        for i, connected in enumerate(connected_sensors):
            status = "✅ Ready" if connected else "❌ Not Ready"
            safe_print(f"Sensor {i+1} (Channel {i}): {status}")
        
        if total_connected == 0:
            safe_print("No sensors ready for high-speed operation")
            return
        
        # Start high-speed data output
        safe_print(f"\nStarting 10ms (100Hz) data output...")
        safe_print("High-speed mode: 8 sensors at 100Hz")
        safe_print("sensor1,sensor2,sensor3,sensor4,sensor5,sensor6,sensor7,sensor8")
        
        # High-precision timing
        last_read_time = time.ticks_us()  # Use microseconds for precision
        sample_interval_us = SAMPLE_RATE_MS * 1000  # Convert to microseconds
        
        # Performance monitoring
        loop_count = 0
        start_time = time.ticks_ms()
        
        try:
            while True:
                current_time = time.ticks_us()
                
                # Check if it's time for next sample
                if time.ticks_diff(current_time, last_read_time) >= sample_interval_us:
                    last_read_time = current_time
                    
                    # Read all sensors as fast as possible
                    values = []
                    for channel in range(NUM_SENSORS):
                        if connected_sensors[channel]:
                            z_g = read_accel_z_fast(i2c, channel)
                        else:
                            z_g = 0.0
                        values.append(f"{z_g:.3f}")
                    
                    # Output data
                    print(f"{','.join(values)}")
                    
                    loop_count += 1
                    
                    # Performance monitoring every 1000 samples
                    if loop_count % 1000 == 0:
                        elapsed_ms = time.ticks_diff(time.ticks_ms(), start_time)
                        actual_rate = (loop_count * 1000) / elapsed_ms
                        safe_print(f"Performance: {actual_rate:.1f}Hz (target: 100Hz)")
                        
                        # Garbage collection every 1000 samples
                        gc.collect()
                
                # Minimal sleep to prevent watchdog timeout
                # Only sleep if we have time before next sample
                time_until_next = sample_interval_us - time.ticks_diff(time.ticks_us(), last_read_time)
                if time_until_next > 1000:  # If more than 1ms until next sample
                    time.sleep_us(500)  # Sleep for 0.5ms
                
        except KeyboardInterrupt:
            safe_print("High-speed operation stopped by user")
            
            # Final performance report
            elapsed_ms = time.ticks_diff(time.ticks_ms(), start_time)
            if elapsed_ms > 0:
                actual_rate = (loop_count * 1000) / elapsed_ms
                safe_print(f"Final performance: {actual_rate:.1f}Hz over {elapsed_ms}ms")
                safe_print(f"Total samples: {loop_count}")
            
        except Exception as e:
            safe_print(f"High-speed runtime error: {e}")
                
    except Exception as e:
        safe_print(f"Main error: {e}")
    
    safe_print("High-speed program ended")

if __name__ == "__main__":
    main()
