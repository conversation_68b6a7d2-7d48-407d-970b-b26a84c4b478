#!/usr/bin/env python3
"""
Simple verification of NodeMCU upload
"""
import serial
import time

def simple_verify(port):
    try:
        print(f"Connecting to {port}...")
        ser = serial.Serial(port, 115200, timeout=5)
        time.sleep(2)
        
        # Interrupt any running program
        ser.write(b'\x03')
        time.sleep(1)
        ser.read_all()
        
        # Check file list
        print("Checking files...")
        ser.write(b'import os; print(os.listdir())\r\n')
        time.sleep(2)
        response = ser.read_all().decode('utf-8', errors='ignore')
        print("Files:", response.strip())
        
        # Check file size
        print("\nChecking file size...")
        ser.write(b'import os; print(os.stat("main.py")[6])\r\n')
        time.sleep(2)
        response = ser.read_all().decode('utf-8', errors='ignore')
        print("File size:", response.strip())
        
        # Read first few lines
        print("\nReading first 5 lines...")
        cmd = b'with open("main.py") as f: [print(f"{i+1}: {line.strip()}") for i, line in enumerate(f.readlines()[:5])]\r\n'
        ser.write(cmd)
        time.sleep(3)
        response = ser.read_all().decode('utf-8', errors='ignore')
        print("First lines:", response.strip())
        
        # Check for key content
        print("\nChecking for key functions...")
        cmd = b'with open("main.py") as f: content=f.read(); print("select_channel" in content, "read_accel_z" in content, "TCA9548A" in content)\r\n'
        ser.write(cmd)
        time.sleep(3)
        response = ser.read_all().decode('utf-8', errors='ignore')
        print("Key functions found:", response.strip())
        
        ser.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    # Compare with original
    try:
        with open('sensor_25ms_no_timestamp.py', 'r') as f:
            original = f.read()
        print(f"Original file: {len(original)} chars, {len(original.splitlines())} lines")
    except:
        print("Could not read original file")
    
    print("\n" + "="*50)
    simple_verify("COM14")
