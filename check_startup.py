#!/usr/bin/env python3
"""
Check startup messages from NodeMCU
"""
import serial
import time

def check_startup(port):
    try:
        print("NodeMCU 시작 메시지 확인 중...")
        ser = serial.Serial(port, 115200, timeout=15)
        time.sleep(2)
        
        # 프로그램 중단
        for i in range(3):
            ser.write(b'\x03')
            time.sleep(0.5)
        
        # 버퍼 클리어
        ser.read_all()
        
        # 소프트 리셋
        print("소프트 리셋 실행...")
        ser.write(b'\x04')  # Ctrl+D
        time.sleep(1)
        
        # 시작 메시지 읽기
        print("시작 메시지 읽는 중...")
        start_time = time.time()
        all_output = []
        
        while time.time() - start_time < 10:
            if ser.in_waiting > 0:
                try:
                    line = ser.readline().decode('utf-8', errors='ignore').strip()
                    if line:
                        all_output.append(line)
                        print(f"  {line}")
                except:
                    pass
            time.sleep(0.1)
        
        ser.close()
        
        print(f"\n총 {len(all_output)}줄의 출력을 받았습니다.")
        
        # 중요한 메시지 찾기
        important_messages = [
            "CPU frequency set to 160MHz",
            "I2C devices found",
            "TCA9548A found",
            "Scanning for sensors",
            "MPU6050 found",
            "initialized successfully",
            "sensor1,sensor2"
        ]
        
        found_messages = []
        for msg in important_messages:
            for line in all_output:
                if msg in line:
                    found_messages.append(msg)
                    break
        
        print(f"\n발견된 중요 메시지: {found_messages}")
        
    except Exception as e:
        print(f"오류: {e}")

if __name__ == "__main__":
    check_startup("COM14")
