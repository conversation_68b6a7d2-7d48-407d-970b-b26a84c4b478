"""
10ms 고속 드럼 센서 모니터링 프로그램 실행 파일 빌드 스크립트
"""
import PyInstaller.__main__
import os
import sys

def build_exe():
    """실행 파일 빌드"""
    try:
        print("=== 10ms 고속 드럼 센서 모니터링 프로그램 빌드 시작 ===")
        
        # 현재 스크립트 경로
        current_dir = os.path.dirname(os.path.abspath(__file__))
        main_script = os.path.join(current_dir, "drum_sensor_10ms_highspeed.py")
        
        # 빌드 옵션
        build_options = [
            main_script,
            '--onefile',  # 단일 실행 파일
            '--windowed',  # 콘솔 창 숨기기
            '--name=DrumSensor_10ms_HighSpeed_v9',
            '--distpath=dist',
            '--workpath=build',
            '--specpath=.',
            '--clean',  # 이전 빌드 파일 정리
            '--noconfirm',  # 덮어쓰기 확인 안함
            
            # 필요한 모듈들 명시적으로 포함
            '--hidden-import=pyqtgraph',
            '--hidden-import=pyqtgraph.Qt',
            '--hidden-import=pyqtgraph.Qt.QtCore',
            '--hidden-import=pyqtgraph.Qt.QtGui',
            '--hidden-import=pyqtgraph.Qt.QtWidgets',
            '--hidden-import=numpy',
            '--hidden-import=serial',
            '--hidden-import=serial.tools.list_ports',
            '--hidden-import=pyautogui',
            '--hidden-import=json',
            '--hidden-import=queue',
            '--hidden-import=threading',
            
            # OpenGL 지원
            '--hidden-import=OpenGL',
            '--hidden-import=OpenGL.GL',
            '--hidden-import=OpenGL.arrays',
            
            # PyQt5/PySide2 지원
            '--hidden-import=PyQt5',
            '--hidden-import=PyQt5.QtCore',
            '--hidden-import=PyQt5.QtGui',
            '--hidden-import=PyQt5.QtWidgets',
            '--hidden-import=PyQt5.QtOpenGL',
            
            # 추가 데이터 파일들
            '--add-data=drum_sensor_10ms_config.json;.' if os.path.exists('drum_sensor_10ms_config.json') else '',
        ]
        
        # 빈 옵션 제거
        build_options = [opt for opt in build_options if opt]
        
        print("빌드 옵션:")
        for option in build_options:
            print(f"  {option}")
        
        print("\n빌드 시작...")
        
        # PyInstaller 실행
        PyInstaller.__main__.run(build_options)
        
        # 빌드 결과 확인
        exe_path = os.path.join(current_dir, "dist", "DrumSensor_10ms_HighSpeed_v9.exe")
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB 단위
            print(f"\n✅ 빌드 성공!")
            print(f"실행 파일 위치: {exe_path}")
            print(f"파일 크기: {file_size:.1f} MB")
            
            # 설정 파일도 dist 폴더에 복사
            config_source = os.path.join(current_dir, "drum_sensor_10ms_config.json")
            config_dest = os.path.join(current_dir, "dist", "drum_sensor_10ms_config.json")
            
            if os.path.exists(config_source):
                import shutil
                shutil.copy2(config_source, config_dest)
                print(f"설정 파일 복사: {config_dest}")
            
            print(f"\n🚀 10ms 고속 드럼 센서 모니터링 프로그램 v9 준비 완료!")
            print(f"키 매핑: a,s,d,f,j,k,l,; (드럼 패드 최적화)")
            print(f"병렬 키 입력: 8개 스레드로 동시 처리")
            print(f"대폭 확장된 Y축 범위: ±10g~±100g (그래프 잘림 완전 해결)")
            print(f"마이너스 임계값: -20g~+20g (양방향 감지)")
            print(f"실행 방법: {exe_path}")
            
        else:
            print(f"\n❌ 빌드 실패: 실행 파일을 찾을 수 없습니다.")
            return False
            
        return True
        
    except Exception as e:
        print(f"\n❌ 빌드 중 오류 발생: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def check_dependencies():
    """필요한 의존성 확인"""
    print("=== 의존성 확인 ===")
    
    required_packages = [
        'PyInstaller',
        'pyqtgraph', 
        'numpy',
        'pyserial',
        'pyautogui',
        'PyQt5'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'pyserial':
                import serial
            elif package == 'PyQt5':
                import PyQt5
            else:
                __import__(package)
            print(f"✅ {package}: 설치됨")
        except ImportError:
            print(f"❌ {package}: 설치되지 않음")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ 누락된 패키지: {', '.join(missing_packages)}")
        print("다음 명령으로 설치하세요:")
        for package in missing_packages:
            if package == 'pyserial':
                print(f"  pip install pyserial")
            else:
                print(f"  pip install {package}")
        return False
    
    print("\n✅ 모든 의존성이 설치되어 있습니다.")
    return True

if __name__ == "__main__":
    print("10ms 고속 드럼 센서 모니터링 프로그램 빌드 도구")
    print("=" * 60)
    
    # 의존성 확인
    if not check_dependencies():
        print("\n의존성을 먼저 설치해주세요.")
        sys.exit(1)
    
    # 빌드 실행
    if build_exe():
        print("\n빌드가 성공적으로 완료되었습니다!")
        
        # 사용법 안내
        print("\n" + "=" * 60)
        print("🎯 사용법:")
        print("1. dist/DrumSensor_10ms_HighSpeed_v9.exe 실행")
        print("2. 시리얼 포트 선택 (NodeMCU 자동 감지)")
        print("3. '연결' 버튼 클릭")
        print("4. 10ms 고속 모드로 8개 센서 모니터링")
        print("5. 마이너스 임계값 조절:")
        print("   - 그래프에서 빨간선 드래그 (-20g~+20g)")
        print("   - 스핀박스로 정밀 조절 (0.01g 단위)")
        print("   - 슬라이더로 빠른 조절")
        print("6. 백그라운드 모드로 키 입력 시뮬레이션")
        print("7. 키 매핑: a,s,d,f,j,k,l,; (드럼 패드 최적화)")
        print("8. 병렬 키 입력: 빠른 연타 시 동시 처리")
        print("9. 대폭 확장된 Y축 범위: ±10g~±100g (그래프 잘림 완전 해결)")
        print("\n🚀 100Hz 고속 실시간 드럼 감지 + 대폭 확장된 Y축 범위!")
        
    else:
        print("\n빌드에 실패했습니다.")
        sys.exit(1)
