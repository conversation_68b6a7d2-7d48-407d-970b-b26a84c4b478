"""
Modified sensor script with debug output
"""
import time
from machine import I2C, Pin, freq

# Configuration
I2C_SCL_PIN = 5  # D1 on NodeMCU
I2C_SDA_PIN = 4  # D2 on NodeMCU
TCA9548A_ADDRESS = 0x70  # Default address of TCA9548A
MPU6050_ADDRESS = 0x68   # Default address of MPU6050
SAMPLE_RATE_MS = 25      # How often to read sensor data (in milliseconds)
NUM_SENSORS = 8          # Total number of sensors to support

# Debug messages
print("===== SCRIPT STARTING =====")
print("Initializing...")

# Try to increase CPU frequency for better performance
try:
    freq(160000000)  # Set to 160MHz for faster processing
    print("CPU frequency set to 160MHz")
except:
    print("Could not set CPU frequency")

# Initialize I2C
print("Initializing I2C...")
i2c = I2C(scl=Pin(I2C_SCL_PIN), sda=Pin(I2C_SDA_PIN), freq=400000)
print("I2C initialized")

# Scan I2C bus
print("Scanning I2C bus...")
devices = i2c.scan()
print("I2C devices found:", [hex(device) for device in devices])

# Check if TCA9548A is found
if TCA9548A_ADDRESS not in devices:
    print("TCA9548A not found. Check your wiring.")
    print("Continuing with test data output...")
    
    # Output header
    print("sensor1,sensor2,sensor3,sensor4,sensor5,sensor6,sensor7,sensor8")
    
    # Generate test data even if no sensors are connected
    count = 0
    last_read_time = time.ticks_ms()
    
    try:
        while True:
            current_time = time.ticks_ms()
            # Only read if it's time to do so
            if time.ticks_diff(current_time, last_read_time) >= SAMPLE_RATE_MS:
                last_read_time = current_time
                
                # Generate test values (sine wave pattern)
                values = []
                for i in range(NUM_SENSORS):
                    # Different phase for each sensor
                    val = (count + i * 10) % 100 / 100.0
                    values.append(f"{val:.3f}")
                
                # Print values
                print(f"{','.join(values)}")
                count += 1
            else:
                # Yield to other tasks
                time.sleep_ms(1)
    except KeyboardInterrupt:
        print("Stopped by user")
else:
    print("TCA9548A found at address 0x70")
    # Original sensor code continues here...
    # (This part will only run if TCA9548A is actually connected)
    
    # For simplicity, we'll also output test data here
    print("Outputting sensor data...")
    print("sensor1,sensor2,sensor3,sensor4,sensor5,sensor6,sensor7,sensor8")
    
    count = 0
    last_read_time = time.ticks_ms()
    
    try:
        while True:
            current_time = time.ticks_ms()
            if time.ticks_diff(current_time, last_read_time) >= SAMPLE_RATE_MS:
                last_read_time = current_time
                
                # Generate test values
                values = []
                for i in range(NUM_SENSORS):
                    val = (count + i * 10) % 100 / 100.0
                    values.append(f"{val:.3f}")
                
                print(f"{','.join(values)}")
                count += 1
            else:
                time.sleep_ms(1)
    except KeyboardInterrupt:
        print("Stopped by user")
