#!/usr/bin/env python3
"""
Reset NodeMCU and monitor 8 sensors initialization
"""
import serial
import time

def reset_and_monitor():
    try:
        print("NodeMCU 리셋 후 8개 센서 초기화 모니터링...")
        ser = serial.Serial("COM14", 115200, timeout=15)
        time.sleep(2)
        
        # 소프트 리셋
        print("소프트 리셋 실행...")
        ser.write(b'\x04')  # Ctrl+D
        time.sleep(2)
        
        print("초기화 과정 모니터링 중...")
        start_time = time.time()
        messages = []
        sensor_count = 0
        
        while time.time() - start_time < 90:  # 1.5분간 모니터링
            if ser.in_waiting > 0:
                try:
                    line = ser.readline().decode('utf-8', errors='ignore').strip()
                    if line:
                        messages.append(line)
                        print(f"  {line}")
                        
                        # 센서 개수 추적
                        if "Total connected sensors:" in line:
                            parts = line.split(":")
                            if len(parts) > 1:
                                try:
                                    sensor_count = int(parts[1].strip())
                                    print(f"🔍 감지된 센서 개수: {sensor_count}")
                                except:
                                    pass
                        
                        # 센서 데이터 시작 감지
                        if "sensor1,sensor2,sensor3,sensor4,sensor5,sensor6,sensor7,sensor8" in line:
                            print("📊 센서 데이터 헤더 감지!")
                            
                        # 실제 센서 데이터 감지
                        if ',' in line and line.count(',') == 7:
                            try:
                                values = line.split(',')
                                if len(values) == 8:
                                    # 0이 아닌 값 개수 세기
                                    non_zero_count = sum(1 for v in values if float(v) != 0.0)
                                    if non_zero_count > 0:
                                        print(f"✅ 활성 센서 데이터: {non_zero_count}개 센서에서 데이터 수신")
                                        # 몇 줄 더 읽기
                                        for i in range(3):
                                            if ser.in_waiting > 0:
                                                line = ser.readline().decode('utf-8', errors='ignore').strip()
                                                if line and ',' in line:
                                                    values = line.split(',')
                                                    if len(values) == 8:
                                                        non_zero = sum(1 for v in values if float(v) != 0.0)
                                                        print(f"  데이터 {i+2}: {non_zero}개 센서 활성")
                                            time.sleep(0.1)
                                        break
                            except:
                                pass
                except:
                    pass
            time.sleep(0.1)
        
        print(f"\n=== 최종 결과 ===")
        print(f"총 메시지: {len(messages)}개")
        print(f"감지된 센서 개수: {sensor_count}개")
        
        # 중요한 정보 추출
        important_info = []
        for msg in messages:
            if any(keyword in msg for keyword in [
                "CPU frequency", "I2C devices", "TCA9548A", 
                "Total connected sensors", "initialized successfully",
                "Failed to initialize"
            ]):
                important_info.append(msg)
        
        print(f"\n중요한 정보:")
        for info in important_info:
            print(f"  {info}")
        
        ser.close()
        
    except Exception as e:
        print(f"오류: {e}")

if __name__ == "__main__":
    reset_and_monitor()
