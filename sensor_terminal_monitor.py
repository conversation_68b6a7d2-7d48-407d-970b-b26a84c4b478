"""
센서 데이터 터미널 모니터

이 프로그램은 NodeMCU에서 전송하는 센서 데이터를 터미널에 표시합니다.
COM 포트를 자동으로 감지하거나 수동으로 선택할 수 있습니다.
"""
import sys
import time
import serial
import serial.tools.list_ports
import argparse
import os
import threading
from datetime import datetime

# 상수 정의
DEFAULT_BAUD_RATE = 115200
DEFAULT_TIMEOUT = 1.0
REFRESH_RATE = 0.1  # 화면 갱신 주기 (초)

class SensorTerminalMonitor:
    def __init__(self, port=None, baud_rate=DEFAULT_BAUD_RATE, timeout=DEFAULT_TIMEOUT, 
                 log_file=None, auto_detect=False):
        self.port = port
        self.baud_rate = baud_rate
        self.timeout = timeout
        self.log_file = log_file
        self.auto_detect = auto_detect
        self.serial_port = None
        self.is_running = False
        self.sensor_values = [0.0] * 8  # 8개 센서 값 저장
        self.timestamp = 0
        self.log_file_handle = None
        
    def detect_port(self):
        """NodeMCU가 연결된 COM 포트 자동 감지"""
        print("사용 가능한 시리얼 포트 검색 중...")
        ports = list(serial.tools.list_ports.comports())
        
        if not ports:
            print("사용 가능한 시리얼 포트가 없습니다.")
            return None
        
        # NodeMCU 자동 감지 (CH340 또는 CP210x 드라이버 사용)
        for port in ports:
            if ('CH340' in port.description or 'CP210x' in port.description or 
                'USB-SERIAL' in port.description or 'USB Serial' in port.description):
                print(f"NodeMCU 감지됨: {port.device} ({port.description})")
                return port.device
        
        # 자동 감지 실패 시 모든 포트 출력
        print("NodeMCU를 자동으로 감지하지 못했습니다. 사용 가능한 포트:")
        for i, port in enumerate(ports):
            print(f"{i+1}. {port.device} - {port.description}")
        
        # 사용자에게 포트 선택 요청
        try:
            selection = input("포트 번호를 선택하세요 (기본값: 1): ").strip()
            if not selection:
                selection = 1
            else:
                selection = int(selection)
            
            if 1 <= selection <= len(ports):
                selected_port = ports[selection-1].device
                print(f"선택된 포트: {selected_port}")
                return selected_port
            else:
                print("잘못된 선택입니다. 첫 번째 포트를 사용합니다.")
                return ports[0].device
        except (ValueError, IndexError):
            print("잘못된 입력입니다. 첫 번째 포트를 사용합니다.")
            return ports[0].device
    
    def connect(self):
        """시리얼 포트 연결"""
        if self.auto_detect or not self.port:
            self.port = self.detect_port()
            if not self.port:
                print("연결할 포트를 찾을 수 없습니다.")
                return False
        
        try:
            print(f"{self.port}에 연결 중... (속도: {self.baud_rate} baud)")
            self.serial_port = serial.Serial(self.port, self.baud_rate, timeout=self.timeout)
            print(f"{self.port}에 연결되었습니다.")
            
            # 로그 파일 설정
            if self.log_file:
                try:
                    self.log_file_handle = open(self.log_file, 'w')
                    self.log_file_handle.write("시간,타임스탬프,센서1,센서2,센서3,센서4,센서5,센서6,센서7,센서8\n")
                    print(f"로그 파일이 생성되었습니다: {self.log_file}")
                except Exception as e:
                    print(f"로그 파일 생성 오류: {str(e)}")
                    self.log_file_handle = None
            
            return True
        except Exception as e:
            print(f"연결 오류: {str(e)}")
            return False
    
    def disconnect(self):
        """연결 해제"""
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()
            print(f"{self.port} 연결이 해제되었습니다.")
        
        if self.log_file_handle:
            self.log_file_handle.close()
            print(f"로그 파일이 저장되었습니다: {self.log_file}")
    
    def process_data(self, data_line):
        """수신된 데이터 처리"""
        try:
            # 데이터 형식: timestamp,sensor1,sensor2,...,sensor8
            parts = data_line.split(',')
            if len(parts) >= 9:  # 타임스탬프 + 8개 센서
                self.timestamp = float(parts[0])
                
                # 센서 데이터 추출
                for i in range(1, 9):
                    try:
                        self.sensor_values[i-1] = float(parts[i])
                    except (ValueError, IndexError):
                        pass  # 변환 오류 무시
                
                # 로그 파일에 기록
                if self.log_file_handle:
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                    log_line = f"{current_time},{self.timestamp}"
                    for value in self.sensor_values:
                        log_line += f",{value:.3f}"
                    self.log_file_handle.write(log_line + "\n")
                    self.log_file_handle.flush()
                
                return True
        except Exception as e:
            print(f"데이터 처리 오류: {str(e)}")
        
        return False
    
    def display_data(self):
        """센서 데이터 화면에 표시"""
        # 화면 지우기 (Windows/Linux/macOS 호환)
        os.system('cls' if os.name == 'nt' else 'clear')
        
        # 헤더 표시
        print("=" * 80)
        print(f"센서 데이터 모니터 - 포트: {self.port}, 속도: {self.baud_rate} baud")
        print("=" * 80)
        print(f"타임스탬프: {self.timestamp:.0f} ms")
        print("-" * 80)
        
        # 센서 데이터 표시
        for i, value in enumerate(self.sensor_values):
            status = "활성" if abs(value) > 0.01 else "비활성"
            print(f"센서 {i+1}: {value:.3f} g  [{status}]")
        
        print("-" * 80)
        print("Ctrl+C를 눌러 종료")
        print("=" * 80)
    
    def read_data_thread(self):
        """데이터 읽기 스레드"""
        buffer = ""
        
        while self.is_running:
            try:
                if self.serial_port and self.serial_port.is_open:
                    # 데이터 읽기
                    data = self.serial_port.read(self.serial_port.in_waiting or 1)
                    if data:
                        buffer += data.decode('utf-8', errors='ignore')
                        
                        # 줄바꿈으로 데이터 분리
                        if '\n' in buffer:
                            lines = buffer.split('\n')
                            buffer = lines[-1]  # 마지막 불완전한 라인은 버퍼에 유지
                            
                            for line in lines[:-1]:
                                if line.strip():  # 빈 라인 무시
                                    self.process_data(line.strip())
                
                time.sleep(0.001)  # CPU 사용량 감소
            except Exception as e:
                print(f"데이터 수신 오류: {str(e)}")
                time.sleep(1)
    
    def run(self):
        """모니터링 시작"""
        if not self.connect():
            return
        
        self.is_running = True
        
        # 데이터 수신 스레드 시작
        read_thread = threading.Thread(target=self.read_data_thread)
        read_thread.daemon = True
        read_thread.start()
        
        try:
            while self.is_running:
                self.display_data()
                time.sleep(REFRESH_RATE)
        except KeyboardInterrupt:
            print("\n프로그램을 종료합니다...")
        finally:
            self.is_running = False
            self.disconnect()

def main():
    """메인 함수"""
    parser = argparse.ArgumentParser(description='센서 데이터 터미널 모니터')
    parser.add_argument('-p', '--port', help='시리얼 포트 (예: COM3, /dev/ttyUSB0)')
    parser.add_argument('-b', '--baud', type=int, default=DEFAULT_BAUD_RATE, help='통신 속도 (기본값: 115200)')
    parser.add_argument('-t', '--timeout', type=float, default=DEFAULT_TIMEOUT, help='타임아웃 (초) (기본값: 1.0)')
    parser.add_argument('-l', '--log', help='로그 파일 경로')
    parser.add_argument('-a', '--auto', action='store_true', help='포트 자동 감지')
    
    args = parser.parse_args()
    
    monitor = SensorTerminalMonitor(
        port=args.port,
        baud_rate=args.baud,
        timeout=args.timeout,
        log_file=args.log,
        auto_detect=args.auto
    )
    
    monitor.run()

if __name__ == "__main__":
    main()
