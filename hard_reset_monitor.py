#!/usr/bin/env python3
"""
Hard reset and monitor full initialization
"""
import serial
import time

def hard_reset_monitor():
    try:
        print("하드 리셋 후 전체 초기화 과정 모니터링...")
        ser = serial.Serial("COM14", 115200, timeout=20)
        time.sleep(2)
        
        # 하드 리셋
        print("하드 리셋 실행...")
        ser.setDTR(False)
        ser.setRTS(True)
        time.sleep(0.1)
        ser.setRTS(False)
        time.sleep(3)
        
        print("부팅 및 초기화 과정 모니터링 중...")
        start_time = time.time()
        messages = []
        initialization_phase = True
        
        while time.time() - start_time < 120:  # 2분간 모니터링
            if ser.in_waiting > 0:
                try:
                    line = ser.readline().decode('utf-8', errors='ignore').strip()
                    if line:
                        messages.append(line)
                        print(f"  {line}")
                        
                        # 초기화 단계별 추적
                        if "Starting power-optimized" in line:
                            print("🚀 전원 최적화 프로그램 시작!")
                            
                        elif "CPU frequency set to" in line:
                            print("⚡ CPU 주파수 설정 완료")
                            
                        elif "I2C devices found:" in line:
                            print("🔍 I2C 장치 스캔 완료")
                            
                        elif "TCA9548A found" in line:
                            print("✅ TCA9548A 멀티플렉서 감지")
                            
                        elif "Powering on sensors sequentially" in line:
                            print("🔋 센서 순차 전원 공급 시작")
                            
                        elif "Initializing sensor on channel" in line:
                            channel = line.split("channel")[-1].strip().replace("---", "").strip()
                            print(f"🔧 채널 {channel} 센서 초기화 중...")
                            
                        elif "initialized successfully" in line:
                            print("✅ 센서 초기화 성공!")
                            
                        elif "Failed to initialize" in line:
                            print("❌ 센서 초기화 실패")
                            
                        elif "Total connected sensors:" in line:
                            count = line.split(":")[-1].strip()
                            print(f"📊 최종 연결된 센서 개수: {count}")
                            
                        elif "sensor1,sensor2,sensor3" in line:
                            print("📈 센서 데이터 출력 시작!")
                            initialization_phase = False
                            
                        # 센서 데이터 단계에서 활성 센서 확인
                        elif not initialization_phase and ',' in line and line.count(',') == 7:
                            try:
                                values = line.split(',')
                                if len(values) == 8:
                                    non_zero_sensors = []
                                    for i, v in enumerate(values):
                                        if float(v) != 0.0:
                                            non_zero_sensors.append(f"센서{i+1}")
                                    
                                    if non_zero_sensors:
                                        print(f"🎯 활성 센서: {', '.join(non_zero_sensors)}")
                                        # 몇 개 더 확인 후 종료
                                        for _ in range(2):
                                            if ser.in_waiting > 0:
                                                line = ser.readline().decode('utf-8', errors='ignore').strip()
                                                if line and ',' in line:
                                                    print(f"  {line}")
                                            time.sleep(0.1)
                                        break
                            except:
                                pass
                except:
                    pass
            time.sleep(0.1)
        
        print(f"\n=== 분석 결과 ===")
        print(f"총 수신 메시지: {len(messages)}개")
        
        # 초기화 관련 메시지 분석
        init_messages = [msg for msg in messages if any(keyword in msg for keyword in [
            "Starting power-optimized", "CPU frequency", "I2C devices", "TCA9548A",
            "Powering on sensors", "Initializing sensor", "initialized successfully",
            "Failed to initialize", "Total connected sensors"
        ])]
        
        print(f"초기화 관련 메시지: {len(init_messages)}개")
        for msg in init_messages:
            print(f"  {msg}")
        
        ser.close()
        
    except Exception as e:
        print(f"오류: {e}")

if __name__ == "__main__":
    hard_reset_monitor()
