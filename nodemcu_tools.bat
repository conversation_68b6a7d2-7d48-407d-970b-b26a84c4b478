@echo off
title NodeMCU 도구 모음
color 0A

:menu
cls
echo ===================================
echo       NodeMCU 도구 모음 v1.0
echo ===================================
echo.
echo  1. 센서 모니터링
echo  2. NodeMCU 리셋 및 모니터링
echo  3. 파일 업로드 및 모니터링
echo  4. 파일 시스템 확인
echo  5. 그래프 애플리케이션 실행
echo  0. 종료
echo.
echo ===================================
echo.

set /p choice=선택하세요 (0-5):

if "%choice%"=="1" goto monitor
if "%choice%"=="2" goto reset_monitor
if "%choice%"=="3" goto upload_monitor
if "%choice%"=="4" goto check_files
if "%choice%"=="5" goto run_graph
if "%choice%"=="0" goto end

echo 잘못된 선택입니다. 다시 시도하세요.
timeout /t 2 >nul
goto menu

:monitor
cls
echo NodeMCU 센서 모니터링 시작...
echo COM13 포트에 연결 중...
echo 종료하려면 Ctrl+C를 누르세요.
echo.

:: 다른 프로세스가 포트를 사용 중인지 확인
tasklist /FI "IMAGENAME eq python.exe" | find "python.exe" > nul
if %ERRORLEVEL% EQU 0 (
    echo 다른 Python 프로세스가 실행 중입니다. 종료 중...
    taskkill /F /IM python.exe > nul 2>&1
    timeout /t 2 > nul
)

:: NodeMCU가 준비될 때까지 잠시 대기
timeout /t 2 > nul

:: 시리얼 모니터 연결
python -m serial.tools.miniterm COM13 115200
goto menu

:reset_monitor
cls
echo NodeMCU 리셋 및 모니터링 시작...
echo.

:: 다른 프로세스가 포트를 사용 중인지 확인
tasklist /FI "IMAGENAME eq python.exe" | find "python.exe" > nul
if %ERRORLEVEL% EQU 0 (
    echo 다른 Python 프로세스가 실행 중입니다. 종료 중...
    taskkill /F /IM python.exe > nul 2>&1
    timeout /t 2 > nul
)

echo NodeMCU 리셋 중...
python -m ampy.cli --port COM13 reset

:: NodeMCU가 부팅될 때까지 잠시 대기
echo NodeMCU 부팅 대기 중...
timeout /t 3 > nul

echo.
echo COM13 포트에 연결 중...
echo 종료하려면 Ctrl+C를 누르세요.
echo.

:: 시리얼 모니터 연결
python -m serial.tools.miniterm COM13 115200
goto menu

:upload_monitor
cls
echo NodeMCU 파일 업로드 및 모니터링 시작...
echo.

:: 다른 프로세스가 포트를 사용 중인지 확인
tasklist /FI "IMAGENAME eq python.exe" | find "python.exe" > nul
if %ERRORLEVEL% EQU 0 (
    echo 다른 Python 프로세스가 실행 중입니다. 종료 중...
    taskkill /F /IM python.exe > nul 2>&1
    timeout /t 2 > nul
)

echo 파일 업로드 중...
python -m ampy.cli --port COM13 put sensor_25ms_no_timestamp.py /main.py

echo.
echo NodeMCU 리셋 중...
python -m ampy.cli --port COM13 reset

:: NodeMCU가 부팅될 때까지 잠시 대기
echo NodeMCU 부팅 대기 중...
timeout /t 3 > nul

echo.
echo COM13 포트에 연결 중...
echo 종료하려면 Ctrl+C를 누르세요.
echo.

:: 시리얼 모니터 연결
python -m serial.tools.miniterm COM13 115200
goto menu

:check_files
cls
echo NodeMCU 파일 시스템 확인...
echo.
python -m ampy.cli --port COM13 ls
echo.
pause
goto menu

:run_graph
cls
echo 그래프 애플리케이션 실행 중...
start "" dist\DrumSensorMonitor_NoTimestamp.exe
goto menu

:end
cls
echo 프로그램을 종료합니다...
timeout /t 2 >nul
exit
