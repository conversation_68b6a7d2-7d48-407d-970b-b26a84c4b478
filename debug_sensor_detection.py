#!/usr/bin/env python3
"""
Debug sensor detection issues
"""
import serial
import time

def debug_sensor_detection(port):
    try:
        print("센서 감지 문제 디버깅...")
        ser = serial.Serial(port, 115200, timeout=10)
        time.sleep(2)
        
        # 현재 실행 중인 프로그램 중단
        print("현재 프로그램 중단...")
        for i in range(5):
            ser.write(b'\x03')  # Ctrl+C
            time.sleep(0.5)
        
        # 버퍼 클리어
        ser.read_all()
        
        # 프롬프트 확인
        ser.write(b'\r\n')
        time.sleep(1)
        response = ser.read_all().decode('utf-8', errors='ignore')
        print(f"프롬프트 응답: {repr(response)}")
        
        # I2C 스캔 테스트
        print("\n=== I2C 스캔 테스트 ===")
        commands = [
            "from machine import I2C, Pin",
            "i2c = I2C(scl=Pin(5), sda=Pin(4), freq=100000)",
            "devices = i2c.scan()",
            "print('I2C devices found:', [hex(d) for d in devices])"
        ]
        
        for cmd in commands:
            print(f"실행: {cmd}")
            ser.write(f"{cmd}\r\n".encode('utf-8'))
            time.sleep(2)
            response = ser.read_all().decode('utf-8', errors='ignore')
            print(f"응답: {repr(response)}")
        
        # TCA9548A 테스트 (만약 있다면)
        print("\n=== TCA9548A 테스트 ===")
        tca_commands = [
            "# TCA9548A 채널 선택 테스트",
            "try:",
            "    i2c.writeto(0x70, bytes([1]))",  # 채널 0 선택
            "    print('TCA9548A channel 0 selected')",
            "    devices = i2c.scan()",
            "    print('Channel 0 devices:', [hex(d) for d in devices])",
            "except Exception as e:",
            "    print('TCA9548A error:', e)"
        ]
        
        for cmd in tca_commands:
            if cmd.startswith('#'):
                print(cmd)
                continue
            ser.write(f"{cmd}\r\n".encode('utf-8'))
            time.sleep(1)
            response = ser.read_all().decode('utf-8', errors='ignore')
            if response.strip():
                print(f"응답: {repr(response)}")
        
        # 직접 MPU6050 테스트
        print("\n=== 직접 MPU6050 테스트 ===")
        mpu_commands = [
            "# 직접 MPU6050 통신 테스트",
            "try:",
            "    who_am_i = i2c.readfrom_mem(0x68, 0x75, 1)",
            "    print('MPU6050 WHO_AM_I:', hex(who_am_i[0]))",
            "except Exception as e:",
            "    print('MPU6050 direct test error:', e)"
        ]
        
        for cmd in mpu_commands:
            if cmd.startswith('#'):
                print(cmd)
                continue
            ser.write(f"{cmd}\r\n".encode('utf-8'))
            time.sleep(1)
            response = ser.read_all().decode('utf-8', errors='ignore')
            if response.strip():
                print(f"응답: {repr(response)}")
        
        # 다른 I2C 주파수로 테스트
        print("\n=== 다른 I2C 주파수 테스트 ===")
        freq_commands = [
            "# 400kHz로 테스트",
            "i2c = I2C(scl=Pin(5), sda=Pin(4), freq=400000)",
            "devices = i2c.scan()",
            "print('400kHz devices:', [hex(d) for d in devices])",
            "",
            "# 50kHz로 테스트",
            "i2c = I2C(scl=Pin(5), sda=Pin(4), freq=50000)",
            "devices = i2c.scan()",
            "print('50kHz devices:', [hex(d) for d in devices])"
        ]
        
        for cmd in freq_commands:
            if cmd.startswith('#') or cmd == "":
                print(cmd)
                continue
            ser.write(f"{cmd}\r\n".encode('utf-8'))
            time.sleep(2)
            response = ser.read_all().decode('utf-8', errors='ignore')
            if response.strip():
                print(f"응답: {repr(response)}")
        
        ser.close()
        
    except Exception as e:
        print(f"디버깅 오류: {e}")

if __name__ == "__main__":
    debug_sensor_detection("COM14")
