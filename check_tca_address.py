#!/usr/bin/env python3
"""
Check TCA9548A address and scan for multiple TCA9548A devices
"""
import serial
import time

def check_tca_address():
    try:
        print("TCA9548A 주소 확인 및 다중 장치 스캔...")
        ser = serial.Serial("COM14", 115200, timeout=10)
        time.sleep(2)
        
        # 프로그램 중단
        for i in range(3):
            ser.write(b'\x03')
            time.sleep(0.5)
        
        ser.read_all()
        
        # I2C 초기화
        commands = [
            "from machine import I2C, Pin",
            "i2c = I2C(scl=Pin(5), sda=Pin(4), freq=100000)",
            "print('I2C initialized')"
        ]
        
        for cmd in commands:
            ser.write(f"{cmd}\r\n".encode('utf-8'))
            time.sleep(1)
        
        response = ser.read_all().decode('utf-8', errors='ignore')
        print(f"초기화: {response}")
        
        # 전체 I2C 스캔
        print("\n=== 전체 I2C 주소 스캔 ===")
        scan_commands = [
            "devices = i2c.scan()",
            "print('All I2C devices:', [hex(d) for d in devices])",
            "",
            "# TCA9548A 가능한 주소들 확인 (0x70-0x77)",
            "tca_addresses = []",
            "for addr in range(0x70, 0x78):",
            "    if addr in devices:",
            "        tca_addresses.append(addr)",
            "        print(f'TCA9548A found at address: {hex(addr)}')",
            "",
            "if not tca_addresses:",
            "    print('No TCA9548A devices found')",
            "else:",
            "    print(f'Total TCA9548A devices: {len(tca_addresses)}')"
        ]
        
        for cmd in scan_commands:
            if cmd == "":
                continue
            ser.write(f"{cmd}\r\n".encode('utf-8'))
            time.sleep(0.3)
        
        time.sleep(2)
        response = ser.read_all().decode('utf-8', errors='ignore')
        if response.strip():
            lines = response.split('\n')
            for line in lines:
                line = line.strip()
                if line and not line.startswith('>>>') and not line.startswith('...'):
                    print(f"  {line}")
        
        # TCA9548A 기능 테스트
        print("\n=== TCA9548A 기능 테스트 ===")
        test_commands = [
            "# TCA9548A 0x70 기능 테스트",
            "tca_addr = 0x70",
            "print(f'Testing TCA9548A at {hex(tca_addr)}')",
            "",
            "# 각 채널 테스트",
            "for channel in range(8):",
            "    try:",
            "        # 채널 선택",
            "        i2c.writeto(tca_addr, bytes([1 << channel]))",
            "        import time",
            "        time.sleep_ms(50)",
            "        # 스캔",
            "        channel_devices = i2c.scan()",
            "        mpu_devices = [d for d in channel_devices if d == 0x68]",
            "        if mpu_devices:",
            "            print(f'Channel {channel}: MPU6050 found')",
            "        else:",
            "            print(f'Channel {channel}: No MPU6050')",
            "    except Exception as e:",
            "        print(f'Channel {channel}: Error - {e}')"
        ]
        
        for cmd in test_commands:
            if cmd == "":
                continue
            ser.write(f"{cmd}\r\n".encode('utf-8'))
            time.sleep(0.2)
        
        time.sleep(5)
        response = ser.read_all().decode('utf-8', errors='ignore')
        if response.strip():
            lines = response.split('\n')
            for line in lines:
                line = line.strip()
                if line and not line.startswith('>>>') and not line.startswith('...'):
                    print(f"  {line}")
        
        # MPU6050 개수 확인
        print("\n=== MPU6050 개수 확인 ===")
        count_commands = [
            "# 모든 채널에서 MPU6050 개수 세기",
            "total_mpu = 0",
            "for channel in range(8):",
            "    try:",
            "        i2c.writeto(0x70, bytes([1 << channel]))",
            "        time.sleep_ms(50)",
            "        devices = i2c.scan()",
            "        mpu_count = devices.count(0x68)",
            "        if mpu_count > 0:",
            "            total_mpu += mpu_count",
            "            print(f'Channel {channel}: {mpu_count} MPU6050(s)')",
            "    except:",
            "        pass",
            "",
            "print(f'Total MPU6050 sensors detected: {total_mpu}')"
        ]
        
        for cmd in count_commands:
            if cmd == "":
                continue
            ser.write(f"{cmd}\r\n".encode('utf-8'))
            time.sleep(0.2)
        
        time.sleep(3)
        response = ser.read_all().decode('utf-8', errors='ignore')
        if response.strip():
            lines = response.split('\n')
            for line in lines:
                line = line.strip()
                if line and not line.startswith('>>>') and not line.startswith('...'):
                    print(f"  {line}")
        
        ser.close()
        
    except Exception as e:
        print(f"오류: {e}")

if __name__ == "__main__":
    check_tca_address()
