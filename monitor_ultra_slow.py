#!/usr/bin/env python3
"""
Monitor ultra-slow initialization process
"""
import serial
import time

def monitor_ultra_slow():
    try:
        print("초저속 초기화 과정 모니터링...")
        print("이 과정은 매우 오래 걸립니다 (각 센서당 약 30초)")
        ser = serial.Serial("COM14", 115200, timeout=30)
        time.sleep(3)
        
        print("초기화 과정 모니터링 중...")
        start_time = time.time()
        messages = []
        sensor_results = {}
        current_sensor = 0
        
        while time.time() - start_time < 600:  # 10분간 모니터링
            if ser.in_waiting > 0:
                try:
                    line = ser.readline().decode('utf-8', errors='ignore').strip()
                    if line:
                        messages.append(line)
                        print(f"  {line}")
                        
                        # 센서 초기화 진행 상황 추적
                        if "INITIALIZING SENSOR" in line and "ON CHANNEL" in line:
                            parts = line.split()
                            for i, part in enumerate(parts):
                                if part == "SENSOR" and i + 1 < len(parts):
                                    try:
                                        current_sensor = int(parts[i + 1])
                                        print(f"🔧 센서 {current_sensor} 초기화 시작!")
                                        break
                                    except:
                                        pass
                        
                        elif "SUCCESSFULLY INITIALIZED" in line:
                            sensor_results[current_sensor] = "SUCCESS"
                            print(f"✅ 센서 {current_sensor} 초기화 성공!")
                            
                        elif "INITIALIZATION FAILED" in line:
                            sensor_results[current_sensor] = "FAILED"
                            print(f"❌ 센서 {current_sensor} 초기화 실패!")
                        
                        elif "Total sensors detected:" in line:
                            total = line.split(":")[-1].strip()
                            print(f"🎯 최종 감지된 센서 개수: {total}")
                            
                        elif "sensor1,sensor2,sensor3" in line:
                            print("📊 데이터 출력 시작!")
                            
                        elif ',' in line and line.count(',') == 7:
                            try:
                                values = line.split(',')
                                if len(values) == 8:
                                    non_zero_count = sum(1 for v in values if float(v) != 0.0)
                                    if non_zero_count > 0:
                                        print(f"📈 활성 센서: {non_zero_count}개")
                                        # 몇 줄 더 읽고 종료
                                        for _ in range(3):
                                            if ser.in_waiting > 0:
                                                line = ser.readline().decode('utf-8', errors='ignore').strip()
                                                if line and ',' in line:
                                                    print(f"  {line}")
                                            time.sleep(0.1)
                                        break
                            except:
                                pass
                        
                        # 진행 상황 표시
                        if "Waiting" in line and "ms" in line:
                            print("⏳ 대기 중...")
                            
                except:
                    pass
            time.sleep(0.1)
        
        print(f"\n=== 초저속 초기화 결과 ===")
        print(f"총 메시지: {len(messages)}개")
        print(f"센서 초기화 결과: {sensor_results}")
        
        success_count = sum(1 for result in sensor_results.values() if result == "SUCCESS")
        print(f"성공한 센서: {success_count}개")
        
        # 중요한 메시지들
        important_messages = [msg for msg in messages if any(keyword in msg for keyword in [
            "ULTRA-SLOW INITIALIZATION", "CPU frequency", "I2C devices", 
            "TCA9548A found", "Total sensors detected", "SUCCESSFULLY INITIALIZED",
            "INITIALIZATION FAILED"
        ])]
        
        print(f"\n중요한 메시지들:")
        for msg in important_messages[-10:]:  # 마지막 10개만
            print(f"  {msg}")
        
        ser.close()
        
    except Exception as e:
        print(f"오류: {e}")

if __name__ == "__main__":
    monitor_ultra_slow()
