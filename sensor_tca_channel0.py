"""
TCA9548A Channel 0 MPU6050 sensor program
Specifically designed for sensor connected to TCA9548A channel 0
"""
import time
from machine import I2C, Pin, freq
import gc

# Configuration
I2C_SCL_PIN = 5  # D1 on NodeMCU
I2C_SDA_PIN = 4  # D2 on NodeMCU
TCA9548A_ADDRESS = 0x70  # Default address of TCA9548A
MPU6050_ADDRESS = 0x68   # Default address of MPU6050
SAMPLE_RATE_MS = 25      # How often to read sensor data (in milliseconds)
NUM_SENSORS = 8          # Total number of sensors to support
INIT_DELAY_MS = 100      # Delay after sensor initialization (in milliseconds)
CHANNEL_SWITCH_DELAY_MS = 20  # Delay after channel switching (in milliseconds)

def safe_print(msg):
    """Safe print function with error handling"""
    try:
        print(msg)
    except:
        pass

def safe_setup():
    """Safe setup with enhanced error handling"""
    try:
        # Try to increase CPU frequency for better performance
        try:
            freq(160000000)  # Set to 160MHz for faster processing
            safe_print("CPU frequency set to 160MHz")
        except Exception as e:
            safe_print(f"Could not set CPU frequency: {e}")

        # Initialize I2C with error handling
        try:
            i2c = I2C(scl=Pin(I2C_SCL_PIN), sda=Pin(I2C_SDA_PIN), freq=100000)
            safe_print("I2C initialized successfully at 100kHz")
            
            # Test I2C communication
            time.sleep_ms(100)
            devices = i2c.scan()
            safe_print(f"I2C scan successful: {[hex(d) for d in devices]}")
            
            return i2c
        except Exception as e:
            safe_print(f"I2C initialization failed: {e}")
            return None
            
    except Exception as e:
        safe_print(f"Setup error: {e}")
        return None

def select_channel(i2c, channel):
    """Select TCA9548A channel with enhanced error handling"""
    try:
        if not 0 <= channel <= 7:
            return False
        
        # Multiple attempts to select channel
        for attempt in range(5):
            try:
                channel_byte = 1 << channel
                safe_print(f"Selecting channel {channel} (byte: {bin(channel_byte)})")
                i2c.writeto(TCA9548A_ADDRESS, bytes([channel_byte]))
                time.sleep_ms(CHANNEL_SWITCH_DELAY_MS)
                
                # Verify channel selection by scanning
                devices = i2c.scan()
                safe_print(f"Channel {channel} scan result: {[hex(d) for d in devices]}")
                return True
            except Exception as e:
                safe_print(f"Channel {channel} selection attempt {attempt + 1} failed: {e}")
                time.sleep_ms(50)
        
        return False
    except Exception as e:
        safe_print(f"Channel selection error for channel {channel}: {e}")
        return False

def initialize_mpu6050_on_channel(i2c, channel):
    """Initialize MPU6050 on specific TCA9548A channel"""
    try:
        safe_print(f"Attempting to initialize MPU6050 on channel {channel}...")
        
        # Select channel
        if not select_channel(i2c, channel):
            safe_print(f"Failed to select channel {channel}")
            return False
        
        # Check if MPU6050 is present
        devices = i2c.scan()
        if MPU6050_ADDRESS not in devices:
            safe_print(f"MPU6050 not found on channel {channel}")
            return False
        
        safe_print(f"MPU6050 found on channel {channel}, initializing...")
        
        # Step 1: Wake up the MPU6050
        safe_print(f"Step 1: Waking up MPU6050 on channel {channel}")
        for attempt in range(5):
            try:
                i2c.writeto_mem(MPU6050_ADDRESS, 0x6B, b'\x00')
                time.sleep_ms(INIT_DELAY_MS)
                break
            except Exception as e:
                safe_print(f"Wake attempt {attempt + 1} failed: {e}")
                time.sleep_ms(100)
        else:
            safe_print(f"Failed to wake up MPU6050 on channel {channel}")
            return False
        
        # Step 2: Set accelerometer range to ±2g
        safe_print(f"Step 2: Setting accelerometer range on channel {channel}")
        try:
            i2c.writeto_mem(MPU6050_ADDRESS, 0x1C, b'\x00')
            time.sleep_ms(INIT_DELAY_MS)
        except Exception as e:
            safe_print(f"Failed to set accelerometer range: {e}")
        
        # Step 3: Set sample rate
        try:
            i2c.writeto_mem(MPU6050_ADDRESS, 0x19, b'\x00')
            time.sleep_ms(50)
        except Exception as e:
            safe_print(f"Failed to set sample rate: {e}")
        
        # Step 4: Set digital low pass filter
        try:
            i2c.writeto_mem(MPU6050_ADDRESS, 0x1A, b'\x01')
            time.sleep_ms(50)
        except Exception as e:
            safe_print(f"Failed to set filter: {e}")
        
        # Step 5: Verify sensor is responding
        safe_print(f"Step 3: Verifying sensor response on channel {channel}")
        for attempt in range(5):
            try:
                who_am_i = i2c.readfrom_mem(MPU6050_ADDRESS, 0x75, 1)
                safe_print(f"Channel {channel} WHO_AM_I: {hex(who_am_i[0])}")
                if who_am_i[0] == 0x68:
                    safe_print(f"MPU6050 on channel {channel} initialized successfully!")
                    return True
                else:
                    safe_print(f"Unexpected WHO_AM_I on channel {channel}: {hex(who_am_i[0])}")
                    # Continue anyway for clones
                    return True
            except Exception as e:
                safe_print(f"WHO_AM_I attempt {attempt + 1} failed: {e}")
                time.sleep_ms(100)
        
        safe_print(f"Could not verify sensor on channel {channel}, but continuing...")
        return True
        
    except Exception as e:
        safe_print(f"Error initializing sensor on channel {channel}: {e}")
        return False

def read_accel_z(i2c, channel):
    """Read Z-axis acceleration from MPU6050 on specific channel"""
    try:
        # Select the channel
        if not select_channel(i2c, channel):
            return 0.0
        
        # Read Z-axis acceleration registers
        data = i2c.readfrom_mem(MPU6050_ADDRESS, 0x3F, 2)
        
        # Convert to 16-bit signed value
        z = (data[0] << 8) | data[1]
        if z > 32767:
            z -= 65536
        
        # Convert to g (±2g range)
        z_g = z / 16384.0
        
        return z_g
    except Exception as e:
        return 0.0

def main():
    """Main function with focus on channel 0"""
    try:
        safe_print("Starting TCA9548A Channel 0 sensor program...")
        
        # Safe setup
        i2c = safe_setup()
        if i2c is None:
            safe_print("Failed to initialize I2C. Exiting.")
            return
        
        # Scan I2C bus
        try:
            devices = i2c.scan()
            safe_print(f"I2C devices found: {[hex(device) for device in devices]}")
        except Exception as e:
            safe_print(f"I2C scan error: {e}")
            devices = []
        
        # Check if TCA9548A is found
        if TCA9548A_ADDRESS not in devices:
            safe_print("TCA9548A not found. Check your wiring.")
            return
        else:
            safe_print("TCA9548A found at address 0x70")
        
        # Initialize sensors array
        connected_sensors = [False] * NUM_SENSORS
        
        # Focus on channel 0 first
        safe_print("\n=== Testing Channel 0 (where sensor is connected) ===")
        if initialize_mpu6050_on_channel(i2c, 0):
            connected_sensors[0] = True
            safe_print("✅ Channel 0 sensor initialized successfully!")
        else:
            safe_print("❌ Failed to initialize channel 0 sensor")
        
        # Test other channels quickly
        safe_print("\n=== Quick scan of other channels ===")
        for channel in range(1, NUM_SENSORS):
            safe_print(f"Testing channel {channel}...")
            if select_channel(i2c, channel):
                devices = i2c.scan()
                if MPU6050_ADDRESS in devices:
                    safe_print(f"Found sensor on channel {channel}")
                    if initialize_mpu6050_on_channel(i2c, channel):
                        connected_sensors[channel] = True
        
        # Display connection status
        safe_print("\nSensor connection status:")
        for i, connected in enumerate(connected_sensors):
            status = "Connected" if connected else "Not Connected"
            safe_print(f"Sensor {i+1} (Channel {i}) status: {status}")
        
        num_connected = sum(connected_sensors)
        safe_print(f"\nTotal connected sensors: {num_connected}")
        
        # Start data output
        safe_print(f"\nReading Z-axis acceleration data at {SAMPLE_RATE_MS}ms intervals (Ctrl+C to stop)...")
        safe_print("sensor1,sensor2,sensor3,sensor4,sensor5,sensor6,sensor7,sensor8")
        
        last_read_time = time.ticks_ms()
        
        try:
            while True:
                current_time = time.ticks_ms()
                if time.ticks_diff(current_time, last_read_time) >= SAMPLE_RATE_MS:
                    last_read_time = current_time
                    
                    values = []
                    for channel in range(NUM_SENSORS):
                        if connected_sensors[channel]:
                            z_g = read_accel_z(i2c, channel)
                        else:
                            z_g = 0.0
                        values.append(f"{z_g:.3f}")
                    
                    try:
                        print(f"{','.join(values)}")
                    except:
                        pass
                    
                    if current_time % 1000 < SAMPLE_RATE_MS:
                        gc.collect()
                else:
                    time.sleep_ms(1)
        except KeyboardInterrupt:
            safe_print("Stopped by user")
        except Exception as e:
            safe_print(f"Runtime error: {e}")
                
    except Exception as e:
        safe_print(f"Main function error: {e}")
    
    safe_print("Program ended")

if __name__ == "__main__":
    main()
