"""
Upload a file to NodeMCU with the correct filename
"""
import sys
import time
import serial
import serial.tools.list_ports

def find_nodemcu_port():
    """Scan for NodeMCU device and return its port"""
    ports = list(serial.tools.list_ports.comports())
    print("Available COM ports:")
    for port in ports:
        print(f"  {port.device} - {port.description}")
        # Look for common NodeMCU identifiers in the description
        if any(id_str in port.description.lower() for id_str in ['cp210x', 'ch340', 'ftdi', 'usb serial', 'silicon labs']):
            print(f"  -> Potential NodeMCU device detected: {port.device}")
            return port.device
    
    return None

def upload_file(file_path, save_as):
    """Upload a file to NodeMCU and save with the specified name"""
    # Find NodeMCU port
    port = find_nodemcu_port()
    if not port:
        print("Error: NodeMCU device not found. Please connect it and try again.")
        return
    
    # Read file content
    try:
        with open(file_path, 'r') as f:
            file_content = f.read()
    except Exception as e:
        print(f"Error reading file: {e}")
        return
    
    try:
        # Connect to the NodeMCU
        print(f"Connecting to {port}...")
        ser = serial.Serial(port, 115200, timeout=1)
        print(f"Connected to {port}")
        
        # Reset the NodeMCU
        print("Resetting NodeMCU...")
        ser.setDTR(False)
        time.sleep(0.1)
        ser.setDTR(True)
        time.sleep(1)
        
        # Clear any pending input
        ser.reset_input_buffer()
        
        # Send Ctrl+C to interrupt any running program
        print("Stopping any running program...")
        ser.write(b'\x03')
        time.sleep(0.1)
        response = ser.read_all().decode('utf-8', errors='ignore')
        print(f"Response: {response}")
        
        # Enter paste mode
        print("Entering paste mode...")
        ser.write(b'\x05')  # Ctrl+E
        time.sleep(0.1)
        response = ser.read_all().decode('utf-8', errors='ignore')
        print(f"Response: {response}")
        
        if 'paste mode' not in response.lower():
            print("Failed to enter paste mode. Make sure MicroPython is installed on the NodeMCU.")
            return
        
        # Send the file content
        print(f"Uploading {file_path} ({len(file_content)} bytes)...")
        for line in file_content.split('\n'):
            ser.write(line.encode('utf-8') + b'\r\n')
            time.sleep(0.01)
        
        # Exit paste mode
        ser.write(b'\x04')  # Ctrl+D
        time.sleep(0.5)
        
        # Read response
        response = ser.read_all().decode('utf-8', errors='ignore')
        print(f"Upload response: {response}")
        
        # Save with the specified name
        print(f"Saving as {save_as}...")
        ser.write(f'with open("{save_as}", "w") as f:\r\n'.encode('utf-8'))
        time.sleep(0.1)
        ser.write(b'    f.write("""' + file_content.encode('utf-8') + b'""")\r\n')
        time.sleep(1)
        
        # Read response
        response = ser.read_all().decode('utf-8', errors='ignore')
        print(f"Save response: {response}")
        
        # List files to verify
        print("Listing files...")
        ser.write(b'import os\r\n')
        time.sleep(0.1)
        ser.write(b'print(os.listdir())\r\n')
        time.sleep(0.5)
        
        # Read response
        response = ser.read_all().decode('utf-8', errors='ignore')
        print(f"Files: {response}")
        
        # Close the connection
        ser.close()
        print(f"File {file_path} uploaded and saved as {save_as}.")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("Usage: python upload_file.py <file_path> <save_as>")
        print("Example: python upload_file.py tca9548a.py tca9548a.py")
        sys.exit(1)
    
    file_path = sys.argv[1]
    save_as = sys.argv[2]
    
    upload_file(file_path, save_as)
