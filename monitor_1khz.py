#!/usr/bin/env python3
"""
Monitor 1kHz I2C sensor initialization
"""
import serial
import time

def monitor_1khz():
    try:
        print("1kHz I2C 센서 초기화 모니터링...")
        ser = serial.Serial("COM14", 115200, timeout=20)
        time.sleep(3)
        
        print("초기화 과정 모니터링 중...")
        start_time = time.time()
        messages = []
        sensor_results = {}
        
        while time.time() - start_time < 300:  # 5분간 모니터링
            if ser.in_waiting > 0:
                try:
                    line = ser.readline().decode('utf-8', errors='ignore').strip()
                    if line:
                        messages.append(line)
                        print(f"  {line}")
                        
                        # 중요한 이벤트 추적
                        if "1kHz I2C Sensor Program" in line:
                            print("🚀 1kHz 센서 프로그램 시작!")
                            
                        elif "I2C devices found:" in line:
                            devices = line.split(":")[-1].strip()
                            print(f"🔍 I2C 장치: {devices}")
                            
                        elif "TCA9548A found" in line:
                            print("✅ TCA9548A 감지됨!")
                            
                        elif "Channel" in line and "initialized successfully" in line:
                            try:
                                channel = int(line.split("Channel")[1].split()[0])
                                sensor_results[channel] = "SUCCESS"
                                print(f"✅ 채널 {channel} 성공!")
                            except:
                                pass
                                
                        elif "failed" in line.lower() and "channel" in line.lower():
                            try:
                                parts = line.split()
                                for i, part in enumerate(parts):
                                    if "channel" in part.lower() and i + 1 < len(parts):
                                        try:
                                            channel = int(parts[i + 1])
                                            sensor_results[channel] = "FAILED"
                                            print(f"❌ 채널 {channel} 실패!")
                                            break
                                        except:
                                            pass
                            except:
                                pass
                        
                        elif "Total sensors at 1kHz:" in line:
                            total = line.split(":")[-1].strip()
                            print(f"🎯 1kHz에서 감지된 센서: {total}개")
                            
                        elif "sensor1,sensor2,sensor3" in line:
                            print("📊 데이터 출력 시작!")
                            
                        elif ',' in line and line.count(',') == 7:
                            try:
                                values = line.split(',')
                                if len(values) == 8:
                                    non_zero_count = sum(1 for v in values if float(v) != 0.0)
                                    if non_zero_count > 0:
                                        active_sensors = []
                                        for i, v in enumerate(values):
                                            if float(v) != 0.0:
                                                active_sensors.append(f"센서{i+1}")
                                        print(f"📈 활성 센서: {', '.join(active_sensors)} ({non_zero_count}개)")
                                        
                                        # 몇 줄 더 읽고 종료
                                        for _ in range(3):
                                            if ser.in_waiting > 0:
                                                line = ser.readline().decode('utf-8', errors='ignore').strip()
                                                if line and ',' in line:
                                                    print(f"  {line}")
                                            time.sleep(0.1)
                                        break
                            except:
                                pass
                        
                except:
                    pass
            time.sleep(0.1)
        
        print(f"\n=== 1kHz I2C 결과 요약 ===")
        print(f"총 메시지: {len(messages)}개")
        print(f"센서 초기화 결과: {sensor_results}")
        
        success_count = sum(1 for result in sensor_results.values() if result == "SUCCESS")
        failed_count = sum(1 for result in sensor_results.values() if result == "FAILED")
        
        print(f"성공한 센서: {success_count}개")
        print(f"실패한 센서: {failed_count}개")
        
        if success_count > 2:
            print(f"🎉 1kHz에서 더 많은 센서 감지! (이전: 2개 → 현재: {success_count}개)")
        elif success_count == 2:
            print("📊 이전과 동일한 센서 개수 (2개)")
        else:
            print("⚠️ 센서 감지 개수 감소")
        
        ser.close()
        
    except Exception as e:
        print(f"오류: {e}")

if __name__ == "__main__":
    monitor_1khz()
