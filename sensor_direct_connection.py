"""
Direct MPU6050 connection (without TCA9548A multiplexer)
For single sensor directly connected to NodeMCU
"""
import time
from machine import I2C, Pin, freq
import gc

# Configuration
I2C_SCL_PIN = 5  # D1 on NodeMCU
I2C_SDA_PIN = 4  # D2 on NodeMCU
MPU6050_ADDRESS = 0x68   # Default address of MPU6050
SAMPLE_RATE_MS = 25      # How often to read sensor data (in milliseconds)
INIT_DELAY_MS = 50       # Delay after sensor initialization (in milliseconds)

def safe_print(msg):
    """Safe print function with error handling"""
    try:
        print(msg)
    except:
        pass

def safe_setup():
    """Safe setup with enhanced error handling"""
    try:
        # Try to increase CPU frequency for better performance
        try:
            freq(160000000)  # Set to 160MHz for faster processing
            safe_print("CPU frequency set to 160MHz")
        except Exception as e:
            safe_print(f"Could not set CPU frequency: {e}")

        # Initialize I2C with error handling
        try:
            i2c = I2C(scl=Pin(I2C_SCL_PIN), sda=Pin(I2C_SDA_PIN), freq=100000)
            safe_print("I2C initialized successfully at 100kHz")
            
            # Test I2C communication
            time.sleep_ms(100)
            devices = i2c.scan()
            safe_print(f"I2C scan successful: {[hex(d) for d in devices]}")
            
            return i2c
        except Exception as e:
            safe_print(f"I2C initialization failed: {e}")
            return None
            
    except Exception as e:
        safe_print(f"Setup error: {e}")
        return None

def initialize_mpu6050(i2c):
    """Initialize MPU6050 directly connected"""
    try:
        safe_print("Initializing directly connected MPU6050...")
        
        # Step 1: Wake up the MPU6050
        safe_print("Step 1: Waking up MPU6050")
        for attempt in range(5):
            try:
                i2c.writeto_mem(MPU6050_ADDRESS, 0x6B, b'\x00')
                break
            except Exception as e:
                safe_print(f"Wake attempt {attempt + 1} failed: {e}")
                time.sleep_ms(100)
        else:
            safe_print("Failed to wake up MPU6050")
            return False
        
        time.sleep_ms(INIT_DELAY_MS)
        
        # Step 2: Set accelerometer range to ±2g
        safe_print("Step 2: Setting accelerometer range")
        try:
            i2c.writeto_mem(MPU6050_ADDRESS, 0x1C, b'\x00')
            time.sleep_ms(INIT_DELAY_MS)
        except Exception as e:
            safe_print(f"Failed to set accelerometer range: {e}")
        
        # Step 3: Set sample rate
        try:
            i2c.writeto_mem(MPU6050_ADDRESS, 0x19, b'\x00')
            time.sleep_ms(50)
        except Exception as e:
            safe_print(f"Failed to set sample rate: {e}")
        
        # Step 4: Set digital low pass filter
        try:
            i2c.writeto_mem(MPU6050_ADDRESS, 0x1A, b'\x01')
            time.sleep_ms(50)
        except Exception as e:
            safe_print(f"Failed to set filter: {e}")
        
        # Step 5: Verify sensor is responding
        safe_print("Step 3: Verifying sensor response")
        for attempt in range(5):
            try:
                who_am_i = i2c.readfrom_mem(MPU6050_ADDRESS, 0x75, 1)
                safe_print(f"WHO_AM_I: {hex(who_am_i[0])}")
                if who_am_i[0] == 0x68:
                    safe_print("MPU6050 initialized successfully!")
                    return True
                else:
                    safe_print(f"Unexpected WHO_AM_I: {hex(who_am_i[0])}")
                    # Continue anyway for clones
                    return True
            except Exception as e:
                safe_print(f"WHO_AM_I attempt {attempt + 1} failed: {e}")
                time.sleep_ms(100)
        
        safe_print("Could not verify sensor, but continuing...")
        return True
        
    except Exception as e:
        safe_print(f"Error initializing MPU6050: {e}")
        return False

def read_accel_z(i2c):
    """Read Z-axis acceleration from directly connected MPU6050"""
    try:
        # Read Z-axis acceleration registers
        data = i2c.readfrom_mem(MPU6050_ADDRESS, 0x3F, 2)
        
        # Convert to 16-bit signed value
        z = (data[0] << 8) | data[1]
        if z > 32767:
            z -= 65536
        
        # Convert to g (±2g range)
        z_g = z / 16384.0
        
        return z_g
    except Exception as e:
        return 0.0

def main():
    """Main function for direct MPU6050 connection"""
    try:
        safe_print("Starting direct MPU6050 sensor program...")
        
        # Safe setup
        i2c = safe_setup()
        if i2c is None:
            safe_print("Failed to initialize I2C. Exiting.")
            return
        
        # Scan I2C bus
        try:
            devices = i2c.scan()
            safe_print(f"I2C devices found: {[hex(device) for device in devices]}")
        except Exception as e:
            safe_print(f"I2C scan error: {e}")
            devices = []
        
        # Check if MPU6050 is found
        if MPU6050_ADDRESS not in devices:
            safe_print("MPU6050 not found. Check your wiring.")
            safe_print("Running in simulation mode...")
            sensor_connected = False
        else:
            safe_print("MPU6050 found at address 0x68")
            # Initialize the sensor
            if initialize_mpu6050(i2c):
                sensor_connected = True
                safe_print("MPU6050 initialized successfully")
            else:
                safe_print("Failed to initialize MPU6050")
                sensor_connected = False
        
        # Start data output
        safe_print(f"\nReading Z-axis acceleration data at {SAMPLE_RATE_MS}ms intervals (Ctrl+C to stop)...")
        safe_print("sensor1,sensor2,sensor3,sensor4,sensor5,sensor6,sensor7,sensor8")
        
        last_read_time = time.ticks_ms()
        
        try:
            while True:
                current_time = time.ticks_ms()
                if time.ticks_diff(current_time, last_read_time) >= SAMPLE_RATE_MS:
                    last_read_time = current_time
                    
                    if sensor_connected:
                        # Read data from connected sensor
                        z_g = read_accel_z(i2c)
                        # Put sensor data in first position, others are 0
                        values = [f"{z_g:.3f}", "0.000", "0.000", "0.000", "0.000", "0.000", "0.000", "0.000"]
                    else:
                        # All zeros if no sensor
                        values = ["0.000", "0.000", "0.000", "0.000", "0.000", "0.000", "0.000", "0.000"]
                    
                    try:
                        print(f"{','.join(values)}")
                    except:
                        pass
                    
                    if current_time % 1000 < SAMPLE_RATE_MS:
                        gc.collect()
                else:
                    time.sleep_ms(1)
        except KeyboardInterrupt:
            safe_print("Stopped by user")
        except Exception as e:
            safe_print(f"Runtime error: {e}")
                
    except Exception as e:
        safe_print(f"Main function error: {e}")
    
    safe_print("Program ended")

if __name__ == "__main__":
    main()
