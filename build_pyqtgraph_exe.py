"""
PyQtGraph 버전 실행 파일 생성 스크립트
"""
import subprocess
import sys

def install_requirements():
    """필요한 라이브러리 설치"""
    requirements = [
        'pyserial',
        'numpy',
        'pyautogui',
        'pyqtgraph',  # 하드웨어 가속 그래프 라이브러리
        'pyopengl',   # OpenGL 지원
        'pyinstaller'  # 실행 파일 생성용
    ]
    
    print("필요한 라이브러리 설치 중...")
    for package in requirements:
        print(f"{package} 설치 중...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"{package} 설치 완료")
        except subprocess.CalledProcessError:
            print(f"{package} 설치 실패")
            return False
    
    print("모든 라이브러리 설치 완료")
    return True

def create_executable():
    """실행 파일 생성"""
    print("실행 파일 생성 중...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--windowed",
            "--name", "DrumSensorMonitor_HW_Accel",
            "--add-data", "README.md;.",
            "drum_sensor_pyqtgraph.py"
        ])
        print("실행 파일 생성 완료")
        print("생성된 실행 파일 위치: dist/DrumSensorMonitor_HW_Accel.exe")
        return True
    except subprocess.CalledProcessError:
        print("실행 파일 생성 실패")
        return False

if __name__ == "__main__":
    print("드럼 센서 모니터링 프로그램 (하드웨어 가속 버전) 설치 도구")
    print("=" * 70)
    
    if install_requirements():
        create = input("실행 파일을 생성하시겠습니까? (y/n): ")
        if create.lower() == 'y':
            create_executable()
    
    print("\n설치 완료. 엔터 키를 눌러 종료하세요.")
    input()
