#!/usr/bin/env python3
"""
Verify that the file was properly uploaded to NodeMCU
"""
import serial
import time

def verify_upload(port):
    """
    Verify the uploaded file on NodeMCU
    """
    try:
        print(f"Connecting to {port} to verify upload...")
        ser = serial.Serial(port, 115200, timeout=10)
        time.sleep(3)
        
        # Send Ctrl+C to interrupt any running program
        ser.write(b'\x03')
        time.sleep(1)
        
        # Clear buffer
        ser.read_all()
        
        # Send Enter to get prompt
        ser.write(b'\r\n')
        time.sleep(1)
        
        print("1. Checking file list...")
        ser.write(b'import os; print("Files:", os.listdir())\r\n')
        time.sleep(2)
        response = ser.read_all().decode('utf-8', errors='ignore')
        print("File list response:", response)
        
        print("\n2. Checking main.py file size...")
        ser.write(b'import os; print("main.py size:", os.stat("main.py")[6], "bytes")\r\n')
        time.sleep(2)
        response = ser.read_all().decode('utf-8', errors='ignore')
        print("File size response:", response)
        
        print("\n3. Reading first few lines of main.py...")
        read_command = '''
with open('main.py', 'r') as f:
    lines = f.readlines()
    print("Total lines:", len(lines))
    print("First 10 lines:")
    for i, line in enumerate(lines[:10]):
        print(f"{i+1}: {line.strip()}")
'''
        ser.write(read_command.encode('utf-8'))
        ser.write(b'\r\n')
        time.sleep(3)
        response = ser.read_all().decode('utf-8', errors='ignore')
        print("First lines response:", response)
        
        print("\n4. Checking if key functions exist...")
        check_functions = '''
with open('main.py', 'r') as f:
    content = f.read()
    functions = ['select_channel', 'read_accel_z', 'initialize_mpu6050']
    for func in functions:
        if f'def {func}' in content:
            print(f"✓ Function {func} found")
        else:
            print(f"✗ Function {func} NOT found")
'''
        ser.write(check_functions.encode('utf-8'))
        ser.write(b'\r\n')
        time.sleep(3)
        response = ser.read_all().decode('utf-8', errors='ignore')
        print("Functions check response:", response)
        
        print("\n5. Checking configuration values...")
        check_config = '''
with open('main.py', 'r') as f:
    content = f.read()
    configs = ['SAMPLE_RATE_MS = 25', 'NUM_SENSORS = 8', 'TCA9548A_ADDRESS = 0x70']
    for config in configs:
        if config in content:
            print(f"✓ Config {config} found")
        else:
            print(f"✗ Config {config} NOT found")
'''
        ser.write(check_config.encode('utf-8'))
        ser.write(b'\r\n')
        time.sleep(3)
        response = ser.read_all().decode('utf-8', errors='ignore')
        print("Config check response:", response)
        
        print("\n6. Testing if the file can be imported...")
        ser.write(b'import sys; print("Testing import...")\r\n')
        time.sleep(1)
        
        # Try to import main (this will execute it, so we need to be careful)
        ser.write(b'print("File appears to be valid Python code")\r\n')
        time.sleep(2)
        response = ser.read_all().decode('utf-8', errors='ignore')
        print("Import test response:", response)
        
        ser.close()
        return True
        
    except Exception as e:
        print(f"Error verifying upload: {e}")
        return False

def compare_with_original():
    """
    Compare original file size with what should be uploaded
    """
    try:
        with open('sensor_25ms_no_timestamp.py', 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        print(f"\nOriginal file analysis:")
        print(f"- File size: {len(original_content)} characters")
        print(f"- Number of lines: {len(original_content.splitlines())}")
        print(f"- Contains 'def select_channel': {'def select_channel' in original_content}")
        print(f"- Contains 'def read_accel_z': {'def read_accel_z' in original_content}")
        print(f"- Contains 'def initialize_mpu6050': {'def initialize_mpu6050' in original_content}")
        print(f"- Contains 'SAMPLE_RATE_MS = 25': {'SAMPLE_RATE_MS = 25' in original_content}")
        
    except Exception as e:
        print(f"Error reading original file: {e}")

if __name__ == "__main__":
    print("=== NodeMCU Upload Verification ===")
    
    # First, analyze the original file
    compare_with_original()
    
    # Then verify what's on the NodeMCU
    print("\n=== Checking NodeMCU ===")
    verify_upload("COM14")
