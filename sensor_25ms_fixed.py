"""
Fixed sensor script with improved MPU6050 initialization
Addresses ENODEV errors during sensor initialization
"""
import time
from machine import I2C, Pin, freq
import gc

# Configuration
I2C_SCL_PIN = 5  # D1 on NodeMCU
I2C_SDA_PIN = 4  # D2 on NodeMCU
TCA9548A_ADDRESS = 0x70  # Default address of TCA9548A
MPU6050_ADDRESS = 0x68   # Default address of MPU6050
SAMPLE_RATE_MS = 25      # How often to read sensor data (in milliseconds)
NUM_SENSORS = 8          # Total number of sensors to support
SCAN_DELAY_MS = 200      # Increased delay for sensor scanning
INIT_DELAY_MS = 100      # Increased delay after sensor initialization
CHANNEL_SWITCH_DELAY_MS = 10  # Increased delay after channel switching

def safe_print(msg):
    """Safe print function with error handling"""
    try:
        print(msg)
    except:
        pass

def safe_i2c_operation(func, *args, max_retries=3, **kwargs):
    """Wrapper for I2C operations with retry logic"""
    for attempt in range(max_retries):
        try:
            return func(*args, **kwargs)
        except OSError as e:
            if e.errno == 19:  # ENODEV
                if attempt < max_retries - 1:
                    time.sleep_ms(50)  # Wait before retry
                    continue
                return None
            elif e.errno == 5:  # EIO
                if attempt < max_retries - 1:
                    time.sleep_ms(50)
                    continue
                return None
            else:
                return None
        except Exception as e:
            if attempt < max_retries - 1:
                time.sleep_ms(50)
                continue
            return None
    return None

def safe_setup():
    """Safe setup with enhanced error handling"""
    try:
        # Try to increase CPU frequency for better performance
        try:
            freq(160000000)  # Set to 160MHz for faster processing
            safe_print("CPU frequency set to 160MHz")
        except Exception as e:
            safe_print(f"Could not set CPU frequency: {e}")

        # Initialize I2C with error handling
        try:
            i2c = I2C(scl=Pin(I2C_SCL_PIN), sda=Pin(I2C_SDA_PIN), freq=100000)  # Reduced frequency for stability
            safe_print("I2C initialized successfully at 100kHz")
            
            # Test I2C communication
            time.sleep_ms(100)  # Allow I2C to stabilize
            devices = safe_i2c_operation(i2c.scan)
            if devices is not None:
                safe_print(f"I2C scan successful: {[hex(d) for d in devices]}")
            else:
                safe_print("I2C scan returned no results")
            
            return i2c
        except Exception as e:
            safe_print(f"I2C initialization failed: {e}")
            return None
            
    except Exception as e:
        safe_print(f"Setup error: {e}")
        return None

def select_channel(i2c, channel):
    """Select TCA9548A channel with enhanced error handling"""
    try:
        if not 0 <= channel <= 7:
            return False
        
        # Multiple attempts to select channel
        for attempt in range(3):
            result = safe_i2c_operation(i2c.writeto, TCA9548A_ADDRESS, bytes([1 << channel]))
            if result is not None:
                time.sleep_ms(CHANNEL_SWITCH_DELAY_MS)
                return True
            time.sleep_ms(20)
        
        return False
    except Exception as e:
        safe_print(f"Channel selection error for channel {channel}: {e}")
        return False

def initialize_mpu6050_robust(i2c, channel):
    """Robust MPU6050 initialization with multiple retry attempts"""
    try:
        safe_print(f"Attempting to initialize MPU6050 on channel {channel}...")
        
        # Select channel with multiple attempts
        channel_selected = False
        for attempt in range(5):
            if select_channel(i2c, channel):
                channel_selected = True
                break
            safe_print(f"Channel selection attempt {attempt + 1} failed, retrying...")
            time.sleep_ms(100)
        
        if not channel_selected:
            safe_print(f"Failed to select channel {channel} after multiple attempts")
            return False
        
        # Step 1: Wake up the MPU6050 with multiple attempts
        safe_print(f"Step 1: Waking up MPU6050 on channel {channel}")
        wake_success = False
        for attempt in range(5):
            result = safe_i2c_operation(i2c.writeto_mem, MPU6050_ADDRESS, 0x6B, b'\x00')
            if result is not None:
                wake_success = True
                break
            safe_print(f"Wake attempt {attempt + 1} failed, retrying...")
            time.sleep_ms(INIT_DELAY_MS)
        
        if not wake_success:
            safe_print(f"Failed to wake up MPU6050 on channel {channel}")
            return False
        
        time.sleep_ms(INIT_DELAY_MS)
        
        # Step 2: Set accelerometer range to ±2g
        safe_print(f"Step 2: Setting accelerometer range on channel {channel}")
        range_success = False
        for attempt in range(3):
            result = safe_i2c_operation(i2c.writeto_mem, MPU6050_ADDRESS, 0x1C, b'\x00')
            if result is not None:
                range_success = True
                break
            time.sleep_ms(50)
        
        if not range_success:
            safe_print(f"Failed to set accelerometer range on channel {channel}")
            # Continue anyway, this is not critical
        
        time.sleep_ms(INIT_DELAY_MS)
        
        # Step 3: Set sample rate (optional)
        safe_i2c_operation(i2c.writeto_mem, MPU6050_ADDRESS, 0x19, b'\x00')
        time.sleep_ms(50)
        
        # Step 4: Set digital low pass filter (optional)
        safe_i2c_operation(i2c.writeto_mem, MPU6050_ADDRESS, 0x1A, b'\x01')
        time.sleep_ms(50)
        
        # Step 5: Verify sensor is responding
        safe_print(f"Step 3: Verifying sensor response on channel {channel}")
        who_am_i = None
        for attempt in range(5):
            who_am_i = safe_i2c_operation(i2c.readfrom_mem, MPU6050_ADDRESS, 0x75, 1)
            if who_am_i is not None:
                break
            safe_print(f"WHO_AM_I read attempt {attempt + 1} failed, retrying...")
            time.sleep_ms(100)
        
        if who_am_i is None:
            safe_print(f"Could not read WHO_AM_I from channel {channel} after multiple attempts")
            return False
            
        if who_am_i[0] != 0x68:
            safe_print(f"Warning: Unexpected WHO_AM_I value on channel {channel}: {hex(who_am_i[0])}")
            # Continue anyway, some clones have different WHO_AM_I values
        
        safe_print(f"MPU6050 on channel {channel} initialized successfully!")
        return True
        
    except Exception as e:
        safe_print(f"Error initializing sensor on channel {channel}: {e}")
        return False

def read_accel_z(i2c, channel):
    """Read Z-axis acceleration with enhanced error handling"""
    try:
        # Select the channel
        if not select_channel(i2c, channel):
            return 0.0
        
        # Read Z-axis acceleration registers with retry
        data = safe_i2c_operation(i2c.readfrom_mem, MPU6050_ADDRESS, 0x3F, 2, max_retries=2)
        if data is None:
            return 0.0
        
        # Convert to 16-bit signed value
        z = (data[0] << 8) | data[1]
        if z > 32767:
            z -= 65536
        
        # Convert to g (±2g range)
        z_g = z / 16384.0
        
        return z_g
    except Exception as e:
        return 0.0

def main():
    """Main function with comprehensive error handling"""
    try:
        safe_print("Starting enhanced sensor program...")
        
        # Safe setup
        i2c = safe_setup()
        if i2c is None:
            safe_print("Failed to initialize I2C. Exiting.")
            return
        
        # Scan I2C bus
        try:
            devices = safe_i2c_operation(i2c.scan)
            if devices is not None:
                safe_print(f"I2C devices found: {[hex(device) for device in devices]}")
            else:
                safe_print("I2C devices found: []")
                devices = []
        except Exception as e:
            safe_print(f"I2C scan error: {e}")
            devices = []
        
        # Check if TCA9548A is found
        if TCA9548A_ADDRESS not in devices:
            safe_print("TCA9548A not found. Check your wiring.")
        else:
            safe_print("TCA9548A found at address 0x70")
        
        # Scan each channel for MPU6050 sensors
        connected_sensors = [False] * NUM_SENSORS
        
        safe_print("Scanning for sensors...")
        for channel in range(NUM_SENSORS):
            try:
                if select_channel(i2c, channel):
                    time.sleep_ms(SCAN_DELAY_MS)
                    
                    channel_devices = safe_i2c_operation(i2c.scan)
                    if channel_devices is not None:
                        safe_print(f"Channel {channel} devices: {[hex(device) for device in channel_devices]}")
                        
                        if MPU6050_ADDRESS in channel_devices:
                            safe_print(f"MPU6050 found on channel {channel}, initializing...")
                            if initialize_mpu6050_robust(i2c, channel):
                                connected_sensors[channel] = True
                                safe_print(f"MPU6050 on channel {channel} initialized successfully")
                            else:
                                safe_print(f"Failed to initialize MPU6050 on channel {channel}")
                    else:
                        safe_print(f"Channel {channel} devices: []")
            except Exception as e:
                safe_print(f"Error scanning channel {channel}: {e}")
        
        # Display connection status
        safe_print("\nSensor connection status:")
        for i, connected in enumerate(connected_sensors):
            status = "Connected" if connected else "Not Connected"
            safe_print(f"Sensor {i+1} status: {status}")
        
        num_connected = sum(connected_sensors)
        safe_print(f"\nTotal connected sensors: {num_connected}")
        
        # Start data output
        safe_print(f"\nReading Z-axis acceleration data at {SAMPLE_RATE_MS}ms intervals (Ctrl+C to stop)...")
        safe_print("sensor1,sensor2,sensor3,sensor4,sensor5,sensor6,sensor7,sensor8")
        
        last_read_time = time.ticks_ms()
        
        try:
            while True:
                current_time = time.ticks_ms()
                if time.ticks_diff(current_time, last_read_time) >= SAMPLE_RATE_MS:
                    last_read_time = current_time
                    
                    values = []
                    for channel in range(NUM_SENSORS):
                        if connected_sensors[channel]:
                            z_g = read_accel_z(i2c, channel)
                        else:
                            z_g = 0.0
                        values.append(f"{z_g:.3f}")
                    
                    try:
                        print(f"{','.join(values)}")
                    except:
                        pass
                    
                    if current_time % 1000 < SAMPLE_RATE_MS:
                        gc.collect()
                else:
                    time.sleep_ms(1)
        except KeyboardInterrupt:
            safe_print("Stopped by user")
        except Exception as e:
            safe_print(f"Runtime error: {e}")
                
    except Exception as e:
        safe_print(f"Main function error: {e}")
    
    safe_print("Program ended")

if __name__ == "__main__":
    main()
