#!/usr/bin/env python3
"""
Test if the program runs after reset
"""
import serial
import time

def test_program_execution(port):
    try:
        print("Connecting to NodeMCU to test program execution...")
        ser = serial.Serial(port, 115200, timeout=15)
        time.sleep(2)
        
        print("Sending reset command...")
        # Send Ctrl+D to soft reset
        ser.write(b'\x04')
        time.sleep(3)
        
        print("Reading boot output...")
        # Read the boot output for 10 seconds
        start_time = time.time()
        output_lines = []
        
        while time.time() - start_time < 10:
            if ser.in_waiting > 0:
                try:
                    line = ser.readline().decode('utf-8', errors='ignore').strip()
                    if line:
                        output_lines.append(line)
                        print(f"  {line}")
                except:
                    pass
            time.sleep(0.1)
        
        ser.close()
        
        # Analyze the output
        print(f"\n=== Analysis ===")
        print(f"Total output lines: {len(output_lines)}")
        
        key_indicators = [
            "CPU frequency set to 160MHz",
            "I2C devices found",
            "TCA9548A",
            "Scanning for sensors",
            "sensor1,sensor2,sensor3"
        ]
        
        found_indicators = []
        for indicator in key_indicators:
            for line in output_lines:
                if indicator in line:
                    found_indicators.append(indicator)
                    break
        
        print(f"Found indicators: {found_indicators}")
        
        if len(found_indicators) >= 2:
            print("✅ Program appears to be running correctly!")
        else:
            print("⚠️  Program may not be running as expected")
            
        return len(found_indicators) >= 2
        
    except Exception as e:
        print(f"Error testing program execution: {e}")
        return False

if __name__ == "__main__":
    print("=== Testing Program Execution ===")
    test_program_execution("COM14")
