"""
Simple test script to verify serial output
"""
import time

# 시작 메시지를 여러 번 출력하여 확인 가능성 높이기
for _ in range(5):
    print("===== STARTING TEST OUTPUT =====")
    time.sleep(0.1)

print("This is a test message")

# 헤더 출력
print("sensor1,sensor2,sensor3,sensor4,sensor5,sensor6,sensor7,sensor8")

# Generate some test data continuously
count = 0
try:
    while True:
        # 매 10번째 출력마다 카운터 표시
        if count % 10 == 0:
            print(f"===== DATA COUNT: {count} =====")

        # 센서 데이터 형식으로 출력
        values = [f"{(count % 10) * 0.1:.3f}" for _ in range(8)]
        print(f"{','.join(values)}")

        count += 1
        time.sleep(0.5)  # 0.5초마다 데이터 출력
except KeyboardInterrupt:
    print("Test stopped by user")
    print("Test completed")
