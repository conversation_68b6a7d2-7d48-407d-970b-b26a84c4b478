"""
Sequential initialization method for 8 MPU6050 sensors
Uses different initialization approach with power cycling and reset sequences
"""
import time
from machine import I2C, Pin, freq
import gc

# Configuration
I2C_SCL_PIN = 5  # D1 on NodeMCU
I2C_SDA_PIN = 4  # D2 on NodeMCU
TCA9548A_ADDRESS = 0x70
MPU6050_ADDRESS = 0x68
SAMPLE_RATE_MS = 25
NUM_SENSORS = 8

def safe_print(msg):
    try:
        print(msg)
    except:
        pass

def setup_i2c():
    """Setup I2C with multiple frequency attempts"""
    frequencies = [50000, 100000, 200000, 400000]
    
    for freq_val in frequencies:
        try:
            safe_print(f"Trying I2C at {freq_val}Hz...")
            i2c = I2C(scl=Pin(I2C_SCL_PIN), sda=Pin(I2C_SDA_PIN), freq=freq_val)
            time.sleep_ms(200)
            
            # Test scan
            devices = i2c.scan()
            if 0x70 in devices:
                safe_print(f"I2C working at {freq_val}Hz - TCA9548A found")
                return i2c, freq_val
            else:
                safe_print(f"No TCA9548A at {freq_val}Hz")
        except Exception as e:
            safe_print(f"I2C failed at {freq_val}Hz: {e}")
    
    return None, 0

def reset_tca9548a(i2c):
    """Reset TCA9548A by disabling all channels"""
    try:
        safe_print("Resetting TCA9548A...")
        # Disable all channels
        i2c.writeto(TCA9548A_ADDRESS, b'\x00')
        time.sleep_ms(100)
        safe_print("TCA9548A reset completed")
        return True
    except Exception as e:
        safe_print(f"TCA9548A reset failed: {e}")
        return False

def power_cycle_channel(i2c, channel):
    """Power cycle a specific channel"""
    try:
        safe_print(f"Power cycling channel {channel}...")
        
        # Disable channel
        i2c.writeto(TCA9548A_ADDRESS, b'\x00')
        time.sleep_ms(500)  # Long delay for power down
        
        # Enable channel
        i2c.writeto(TCA9548A_ADDRESS, bytes([1 << channel]))
        time.sleep_ms(1000)  # Long delay for power up
        
        return True
    except Exception as e:
        safe_print(f"Power cycle failed for channel {channel}: {e}")
        return False

def advanced_mpu6050_init(i2c, channel):
    """Advanced MPU6050 initialization with multiple reset methods"""
    try:
        safe_print(f"Advanced init for channel {channel}...")
        
        # Method 1: Power cycle the channel first
        if not power_cycle_channel(i2c, channel):
            return False
        
        # Check if device is present
        devices = i2c.scan()
        if MPU6050_ADDRESS not in devices:
            safe_print(f"No MPU6050 on channel {channel} after power cycle")
            return False
        
        safe_print(f"MPU6050 detected on channel {channel}")
        
        # Method 2: Multiple reset attempts
        for reset_attempt in range(5):
            try:
                safe_print(f"Reset attempt {reset_attempt + 1} for channel {channel}")
                
                # Step 1: Device reset
                i2c.writeto_mem(MPU6050_ADDRESS, 0x6B, b'\x80')  # Device reset
                time.sleep_ms(200)
                
                # Step 2: Wake up
                i2c.writeto_mem(MPU6050_ADDRESS, 0x6B, b'\x00')  # Wake up
                time.sleep_ms(200)
                
                # Step 3: Signal path reset
                i2c.writeto_mem(MPU6050_ADDRESS, 0x68, b'\x07')  # Signal path reset
                time.sleep_ms(200)
                
                # Step 4: Configure power management
                i2c.writeto_mem(MPU6050_ADDRESS, 0x6B, b'\x01')  # Use PLL with X axis gyroscope reference
                time.sleep_ms(100)
                
                # Step 5: Configure accelerometer
                i2c.writeto_mem(MPU6050_ADDRESS, 0x1C, b'\x00')  # ±2g range
                time.sleep_ms(100)
                
                # Step 6: Configure gyroscope (then disable to save power)
                i2c.writeto_mem(MPU6050_ADDRESS, 0x1B, b'\x00')  # ±250°/s range
                time.sleep_ms(100)
                
                # Step 7: Disable gyroscope to save power
                i2c.writeto_mem(MPU6050_ADDRESS, 0x6C, b'\x07')  # Disable gyro
                time.sleep_ms(100)
                
                # Step 8: Set sample rate
                i2c.writeto_mem(MPU6050_ADDRESS, 0x19, b'\x09')  # Sample rate divider
                time.sleep_ms(100)
                
                # Step 9: Configure DLPF
                i2c.writeto_mem(MPU6050_ADDRESS, 0x1A, b'\x06')  # DLPF 5Hz
                time.sleep_ms(100)
                
                # Step 10: Verify WHO_AM_I
                who_am_i = i2c.readfrom_mem(MPU6050_ADDRESS, 0x75, 1)
                safe_print(f"Channel {channel} WHO_AM_I: {hex(who_am_i[0])}")
                
                # Step 11: Test accelerometer read
                accel_data = i2c.readfrom_mem(MPU6050_ADDRESS, 0x3B, 6)
                safe_print(f"Channel {channel} accel test read successful")
                
                safe_print(f"✅ Channel {channel} initialized successfully!")
                return True
                
            except Exception as e:
                safe_print(f"Reset attempt {reset_attempt + 1} failed for channel {channel}: {e}")
                time.sleep_ms(300)
        
        safe_print(f"❌ All reset attempts failed for channel {channel}")
        return False
        
    except Exception as e:
        safe_print(f"Advanced init failed for channel {channel}: {e}")
        return False

def scan_all_channels_detailed(i2c):
    """Detailed scan of all channels with multiple methods"""
    safe_print("\n=== Detailed Channel Scan ===")
    found_sensors = []
    
    for channel in range(NUM_SENSORS):
        safe_print(f"\n--- Scanning Channel {channel} ---")
        
        # Method 1: Direct channel selection
        try:
            i2c.writeto(TCA9548A_ADDRESS, bytes([1 << channel]))
            time.sleep_ms(100)
            devices = i2c.scan()
            safe_print(f"Channel {channel} direct scan: {[hex(d) for d in devices]}")
            
            if MPU6050_ADDRESS in devices:
                found_sensors.append(channel)
                safe_print(f"✅ MPU6050 found on channel {channel}")
            else:
                safe_print(f"❌ No MPU6050 on channel {channel}")
                
        except Exception as e:
            safe_print(f"Channel {channel} scan error: {e}")
        
        # Method 2: Multiple scan attempts
        for attempt in range(3):
            try:
                i2c.writeto(TCA9548A_ADDRESS, bytes([1 << channel]))
                time.sleep_ms(50 * (attempt + 1))  # Increasing delay
                devices = i2c.scan()
                if MPU6050_ADDRESS in devices and channel not in found_sensors:
                    found_sensors.append(channel)
                    safe_print(f"✅ MPU6050 found on channel {channel} (attempt {attempt + 1})")
                    break
            except:
                pass
    
    safe_print(f"\nFound sensors on channels: {found_sensors}")
    return found_sensors

def read_accel_z_robust(i2c, channel):
    """Robust accelerometer reading with error recovery"""
    try:
        # Select channel with retry
        for attempt in range(3):
            try:
                i2c.writeto(TCA9548A_ADDRESS, bytes([1 << channel]))
                time.sleep_ms(10)
                break
            except:
                if attempt == 2:
                    return 0.0
                time.sleep_ms(20)
        
        # Read with retry
        for attempt in range(2):
            try:
                data = i2c.readfrom_mem(MPU6050_ADDRESS, 0x3F, 2)
                z = (data[0] << 8) | data[1]
                if z > 32767:
                    z -= 65536
                z_g = z / 16384.0
                return z_g
            except:
                if attempt == 1:
                    return 0.0
                time.sleep_ms(10)
        
        return 0.0
    except:
        return 0.0

def main():
    """Main function with sequential initialization"""
    try:
        safe_print("=== Sequential Initialization Method ===")
        
        # Set conservative CPU frequency
        freq(80000000)
        safe_print("CPU frequency set to 80MHz")
        
        # Setup I2C with best frequency
        i2c, best_freq = setup_i2c()
        if i2c is None:
            safe_print("Failed to initialize I2C")
            return
        
        safe_print(f"Using I2C at {best_freq}Hz")
        
        # Reset TCA9548A
        if not reset_tca9548a(i2c):
            safe_print("TCA9548A reset failed")
            return
        
        # Detailed scan first
        found_channels = scan_all_channels_detailed(i2c)
        
        # Initialize found sensors
        connected_sensors = [False] * NUM_SENSORS
        
        safe_print("\n=== Sequential Sensor Initialization ===")
        for channel in found_channels:
            safe_print(f"\nInitializing sensor on channel {channel}...")
            if advanced_mpu6050_init(i2c, channel):
                connected_sensors[channel] = True
                safe_print(f"✅ Sensor {channel + 1} ready!")
            else:
                safe_print(f"❌ Sensor {channel + 1} failed!")
            
            # Delay between initializations
            time.sleep_ms(2000)
            gc.collect()
        
        # Final status
        total_connected = sum(connected_sensors)
        safe_print(f"\n=== Final Status ===")
        safe_print(f"Total sensors initialized: {total_connected}")
        
        for i, connected in enumerate(connected_sensors):
            status = "✅ Connected" if connected else "❌ Not Connected"
            safe_print(f"Sensor {i+1} (Channel {i}): {status}")
        
        if total_connected == 0:
            safe_print("No sensors initialized. Check hardware connections.")
            return
        
        # Start data output
        safe_print(f"\nStarting data output at {SAMPLE_RATE_MS}ms intervals...")
        safe_print("sensor1,sensor2,sensor3,sensor4,sensor5,sensor6,sensor7,sensor8")
        
        last_read_time = time.ticks_ms()
        
        while True:
            current_time = time.ticks_ms()
            if time.ticks_diff(current_time, last_read_time) >= SAMPLE_RATE_MS:
                last_read_time = current_time
                
                values = []
                for channel in range(NUM_SENSORS):
                    if connected_sensors[channel]:
                        z_g = read_accel_z_robust(i2c, channel)
                    else:
                        z_g = 0.0
                    values.append(f"{z_g:.3f}")
                
                print(f"{','.join(values)}")
                
                if current_time % 10000 < SAMPLE_RATE_MS:
                    gc.collect()
            else:
                time.sleep_ms(2)
                
    except KeyboardInterrupt:
        safe_print("Stopped by user")
    except Exception as e:
        safe_print(f"Main error: {e}")

if __name__ == "__main__":
    main()
