#!/usr/bin/env python3
"""
Test NodeMCU on COM13
"""
import serial
import time

def test_com13():
    try:
        print("COM13에서 NodeMCU 테스트...")
        ser = serial.Serial("COM13", 115200, timeout=15)
        time.sleep(3)
        
        print("부팅 메시지 읽는 중...")
        start_time = time.time()
        messages = []
        
        while time.time() - start_time < 12:
            if ser.in_waiting > 0:
                try:
                    line = ser.readline().decode('utf-8', errors='ignore').strip()
                    if line:
                        messages.append(line)
                        print(f"  {line}")
                        
                        # 센서 데이터 감지
                        if ',' in line and line.count(',') == 7:
                            try:
                                values = line.split(',')
                                if len(values) == 8:
                                    print("✅ 센서 데이터 감지!")
                                    # 몇 줄 더 읽기
                                    for _ in range(5):
                                        if ser.in_waiting > 0:
                                            line = ser.readline().decode('utf-8', errors='ignore').strip()
                                            if line:
                                                print(f"  {line}")
                                        time.sleep(0.1)
                                    break
                            except:
                                pass
                except:
                    pass
            time.sleep(0.1)
        
        print(f"\n총 {len(messages)}개의 메시지를 받았습니다.")
        
        # 중요한 키워드 찾기
        keywords = ["CPU frequency", "I2C devices", "TCA9548A", "MPU6050", "sensor1,sensor2", "initialized"]
        found = []
        for keyword in keywords:
            for msg in messages:
                if keyword in msg:
                    found.append(keyword)
                    break
        
        print(f"발견된 키워드: {found}")
        
        if len(found) >= 3:
            print("✅ 센서 프로그램이 정상적으로 실행되고 있습니다!")
        else:
            print("⚠️ 센서 프로그램 실행 상태를 확인할 수 없습니다.")
        
        ser.close()
        
    except Exception as e:
        print(f"오류: {e}")

if __name__ == "__main__":
    test_com13()
