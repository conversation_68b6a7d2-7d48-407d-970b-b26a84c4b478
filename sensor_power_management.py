"""
Power management optimized sensor program
Reduces power consumption and manages sensor power cycling
"""
import time
from machine import I2C, Pin, freq
import gc

# Configuration
I2C_SCL_PIN = 5  # D1 on NodeMCU
I2C_SDA_PIN = 4  # D2 on NodeMCU
TCA9548A_ADDRESS = 0x70
MPU6050_ADDRESS = 0x68
SAMPLE_RATE_MS = 25
NUM_SENSORS = 8

# Power management settings
I2C_FREQUENCY = 50000        # 50kHz - balance between speed and power
POWER_CYCLE_DELAY = 5000     # 5 seconds between power cycles
INIT_DELAY = 2000           # 2 seconds for initialization
CHANNEL_SWITCH_DELAY = 100   # 100ms for channel switching

def safe_print(msg):
    try:
        print(msg)
    except:
        pass

def power_optimized_setup():
    """Setup with power optimization"""
    try:
        # Reduce CPU frequency to save power
        freq(80000000)  # 80MHz instead of 160MHz
        safe_print("CPU frequency reduced to 80MHz for power saving")
        
        # Initialize I2C at moderate frequency
        i2c = I2C(scl=Pin(I2C_SCL_PIN), sda=Pin(I2C_SDA_PIN), freq=I2C_FREQUENCY)
        safe_print(f"I2C initialized at {I2C_FREQUENCY}Hz")
        
        time.sleep_ms(1000)
        
        # Test scan
        devices = i2c.scan()
        safe_print(f"I2C devices found: {[hex(d) for d in devices]}")
        
        return i2c
    except Exception as e:
        safe_print(f"Setup error: {e}")
        return None

def power_cycle_all_channels(i2c):
    """Power cycle all channels to reset sensors"""
    try:
        safe_print("Power cycling all channels...")
        
        # Disable all channels (power down)
        i2c.writeto(TCA9548A_ADDRESS, b'\x00')
        safe_print("All channels disabled - sensors powered down")
        time.sleep_ms(POWER_CYCLE_DELAY)
        
        # Enable all channels briefly (power up)
        i2c.writeto(TCA9548A_ADDRESS, b'\xFF')
        safe_print("All channels enabled - sensors powered up")
        time.sleep_ms(2000)
        
        # Disable all channels again
        i2c.writeto(TCA9548A_ADDRESS, b'\x00')
        time.sleep_ms(1000)
        
        safe_print("Power cycle completed")
        return True
    except Exception as e:
        safe_print(f"Power cycle failed: {e}")
        return False

def gentle_channel_select(i2c, channel):
    """Gentle channel selection with power management"""
    try:
        # Disable all channels first
        i2c.writeto(TCA9548A_ADDRESS, b'\x00')
        time.sleep_ms(50)
        
        # Enable only target channel
        i2c.writeto(TCA9548A_ADDRESS, bytes([1 << channel]))
        time.sleep_ms(CHANNEL_SWITCH_DELAY)
        
        return True
    except Exception as e:
        safe_print(f"Channel {channel} selection failed: {e}")
        return False

def low_power_mpu6050_init(i2c, channel):
    """Low power MPU6050 initialization"""
    try:
        safe_print(f"Low power init for channel {channel}...")
        
        # Select channel
        if not gentle_channel_select(i2c, channel):
            return False
        
        # Check if sensor is present
        devices = i2c.scan()
        if MPU6050_ADDRESS not in devices:
            safe_print(f"No MPU6050 on channel {channel}")
            return False
        
        safe_print(f"MPU6050 detected on channel {channel}")
        
        # Gentle wake up sequence
        try:
            # Wake up (don't reset to avoid power surge)
            i2c.writeto_mem(MPU6050_ADDRESS, 0x6B, b'\x00')
            time.sleep_ms(INIT_DELAY)
            
            # Configure for low power
            # Set accelerometer to ±2g (lowest power)
            i2c.writeto_mem(MPU6050_ADDRESS, 0x1C, b'\x00')
            time.sleep_ms(500)
            
            # Disable gyroscope to save power
            i2c.writeto_mem(MPU6050_ADDRESS, 0x6C, b'\x07')
            time.sleep_ms(500)
            
            # Set lowest sample rate
            i2c.writeto_mem(MPU6050_ADDRESS, 0x19, b'\x0F')
            time.sleep_ms(500)
            
            # Set lowest bandwidth filter
            i2c.writeto_mem(MPU6050_ADDRESS, 0x1A, b'\x06')
            time.sleep_ms(500)
            
            # Enable cycle mode for power saving
            i2c.writeto_mem(MPU6050_ADDRESS, 0x6B, b'\x20')
            time.sleep_ms(500)
            
        except Exception as e:
            safe_print(f"Configuration failed: {e}")
        
        # Test read
        try:
            data = i2c.readfrom_mem(MPU6050_ADDRESS, 0x3F, 2)
            safe_print(f"Channel {channel} test read successful")
        except Exception as e:
            safe_print(f"Test read failed: {e}")
            return False
        
        safe_print(f"✅ Channel {channel} low power init completed!")
        return True
        
    except Exception as e:
        safe_print(f"Low power init failed for channel {channel}: {e}")
        return False

def read_accel_z_power_managed(i2c, channel):
    """Power managed accelerometer reading"""
    try:
        # Select channel
        if not gentle_channel_select(i2c, channel):
            return 0.0
        
        # Wake up sensor for reading
        try:
            i2c.writeto_mem(MPU6050_ADDRESS, 0x6B, b'\x00')
            time.sleep_ms(10)
        except:
            pass
        
        # Read data
        data = i2c.readfrom_mem(MPU6050_ADDRESS, 0x3F, 2)
        z = (data[0] << 8) | data[1]
        if z > 32767:
            z -= 65536
        z_g = z / 16384.0
        
        # Put sensor back to sleep
        try:
            i2c.writeto_mem(MPU6050_ADDRESS, 0x6B, b'\x20')
        except:
            pass
        
        return z_g
    except:
        return 0.0

def scan_with_power_cycling(i2c):
    """Scan sensors with power cycling to detect unstable connections"""
    safe_print("\n=== Power Cycling Sensor Detection ===")
    
    detected_sensors = set()
    
    # Multiple scan cycles
    for cycle in range(5):
        safe_print(f"\nScan cycle {cycle + 1}/5")
        
        # Power cycle
        power_cycle_all_channels(i2c)
        
        # Scan each channel
        cycle_detections = []
        for channel in range(NUM_SENSORS):
            if gentle_channel_select(i2c, channel):
                time.sleep_ms(200)  # Extra time for power stabilization
                devices = i2c.scan()
                if MPU6050_ADDRESS in devices:
                    cycle_detections.append(channel)
                    detected_sensors.add(channel)
                    safe_print(f"Cycle {cycle + 1}: Channel {channel} - MPU6050 detected")
        
        safe_print(f"Cycle {cycle + 1} detections: {cycle_detections}")
        time.sleep_ms(2000)  # Rest between cycles
    
    safe_print(f"\nAll detected sensors across cycles: {sorted(detected_sensors)}")
    return sorted(detected_sensors)

def main():
    """Main function with power management"""
    try:
        safe_print("=== Power Management Sensor Program ===")
        
        # Setup
        i2c = power_optimized_setup()
        if i2c is None:
            safe_print("Failed to initialize I2C")
            return
        
        # Check TCA9548A
        devices = i2c.scan()
        if TCA9548A_ADDRESS not in devices:
            safe_print("TCA9548A not found")
            return
        
        safe_print("TCA9548A found")
        
        # Power cycling detection
        all_detected = scan_with_power_cycling(i2c)
        safe_print(f"\nSensors detected with power cycling: {len(all_detected)}")
        
        # Initialize detected sensors
        connected_sensors = [False] * NUM_SENSORS
        
        safe_print("\n=== Low Power Sensor Initialization ===")
        for channel in all_detected:
            safe_print(f"\n--- Initializing Channel {channel} ---")
            
            if low_power_mpu6050_init(i2c, channel):
                connected_sensors[channel] = True
                safe_print(f"✅ Sensor {channel + 1} ready!")
            else:
                safe_print(f"❌ Sensor {channel + 1} failed!")
            
            time.sleep_ms(3000)  # Long delay between initializations
            gc.collect()
        
        # Final status
        total_connected = sum(connected_sensors)
        safe_print(f"\n=== Power Management Results ===")
        safe_print(f"Total sensors initialized: {total_connected}")
        
        for i, connected in enumerate(connected_sensors):
            status = "✅ Connected" if connected else "❌ Not Connected"
            safe_print(f"Sensor {i+1} (Channel {i}): {status}")
        
        if total_connected == 0:
            safe_print("No sensors initialized with power management")
            return
        
        # Start data output
        safe_print(f"\nStarting power-managed data output...")
        safe_print("sensor1,sensor2,sensor3,sensor4,sensor5,sensor6,sensor7,sensor8")
        
        last_read_time = time.ticks_ms()
        
        while True:
            current_time = time.ticks_ms()
            if time.ticks_diff(current_time, last_read_time) >= SAMPLE_RATE_MS:
                last_read_time = current_time
                
                values = []
                for channel in range(NUM_SENSORS):
                    if connected_sensors[channel]:
                        z_g = read_accel_z_power_managed(i2c, channel)
                    else:
                        z_g = 0.0
                    values.append(f"{z_g:.3f}")
                
                print(f"{','.join(values)}")
                
                if current_time % 15000 < SAMPLE_RATE_MS:
                    gc.collect()
            else:
                time.sleep_ms(5)
                
    except KeyboardInterrupt:
        safe_print("Stopped by user")
    except Exception as e:
        safe_print(f"Main error: {e}")

if __name__ == "__main__":
    main()
