"""
드럼 센서 모니터링 및 키 입력 시뮬레이션 프로그램 (PyQtGraph 버전)

이 프로그램은 NodeMCU에서 전송하는 8개 센서 데이터를 그래프로 표시하고,
임계값을 초과하는 타격을 감지하여 키보드 입력을 시뮬레이션합니다.
PyQtGraph를 사용하여 하드웨어 가속을 지원합니다.
"""
import sys
import time
import threading
import queue
import numpy as np
import serial
import serial.tools.list_ports
import pyautogui  # 키보드 입력 시뮬레이션용

# PyQtGraph 설정
import pyqtgraph as pg
from pyqtgraph.Qt import QtCore, QtGui, QtWidgets

# 상수 정의
MAX_DATA_POINTS = 100  # 그래프에 표시할 최대 데이터 포인트 수
DEFAULT_THRESHOLD = 1.2  # 기본 임계값 (g)
SERIAL_TIMEOUT = 0.1  # 시리얼 타임아웃 (초)
COOLDOWN_TIME = 0.1  # 타격 감지 후 쿨다운 시간 (초)
UPDATE_INTERVAL = 10  # 그래프 업데이트 간격 (ms)

# 하드웨어 가속 활성화
pg.setConfigOptions(antialias=True, useOpenGL=True)

class DrumSensorApp(QtWidgets.QMainWindow):
    def __init__(self):
        super().__init__()
        
        # 변수 초기화
        self.serial_port = None
        self.is_running = False
        self.data_queue = queue.Queue()
        self.sensor_data = [np.zeros(MAX_DATA_POINTS) for _ in range(8)]  # 8개 센서 데이터 저장
        self.timestamps = np.zeros(MAX_DATA_POINTS)  # 타임스탬프 저장
        self.thresholds = [DEFAULT_THRESHOLD] * 8  # 8개 센서 임계값
        self.last_trigger_time = [0] * 8  # 마지막 트리거 시간
        self.hit_indicators = [False] * 8  # 타격 표시
        self.data_index = 0  # 현재 데이터 인덱스
        
        # UI 설정
        self.setWindowTitle("드럼 센서 모니터링 (하드웨어 가속)")
        self.resize(1200, 800)
        self.setup_ui()
        
        # 시리얼 포트 자동 감지
        self.detect_serial_ports()
        
        # 타이머 설정
        self.timer = QtCore.QTimer()
        self.timer.timeout.connect(self.update_plots)
        self.timer.start(UPDATE_INTERVAL)
    
    def setup_ui(self):
        # 메인 위젯 및 레이아웃
        central_widget = QtWidgets.QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QtWidgets.QVBoxLayout(central_widget)
        
        # 컨트롤 패널
        control_panel = QtWidgets.QHBoxLayout()
        main_layout.addLayout(control_panel)
        
        # 시리얼 포트 선택
        control_panel.addWidget(QtWidgets.QLabel("시리얼 포트:"))
        self.port_combo = QtWidgets.QComboBox()
        control_panel.addWidget(self.port_combo)
        
        # 새로고침 버튼
        refresh_btn = QtWidgets.QPushButton("새로고침")
        refresh_btn.clicked.connect(self.detect_serial_ports)
        control_panel.addWidget(refresh_btn)
        
        # 연결/연결 해제 버튼
        self.connect_button = QtWidgets.QPushButton("연결")
        self.connect_button.clicked.connect(self.toggle_connection)
        control_panel.addWidget(self.connect_button)
        
        # 백그라운드 모드 체크박스
        self.background_var = QtWidgets.QCheckBox("백그라운드 모드")
        control_panel.addWidget(self.background_var)
        
        # 스페이서 추가
        control_panel.addStretch(1)
        
        # 임계값 조절 패널
        threshold_panel = QtWidgets.QGroupBox("임계값 조절")
        threshold_layout = QtWidgets.QHBoxLayout(threshold_panel)
        main_layout.addWidget(threshold_panel)
        
        # 각 센서별 임계값 슬라이더
        self.threshold_sliders = []
        self.threshold_labels = []
        
        for i in range(8):
            sensor_frame = QtWidgets.QVBoxLayout()
            threshold_layout.addLayout(sensor_frame)
            
            sensor_frame.addWidget(QtWidgets.QLabel(f"센서 {i+1}"))
            
            # 값 표시 레이블
            value_label = QtWidgets.QLabel(f"{self.thresholds[i]:.1f}")
            sensor_frame.addWidget(value_label)
            self.threshold_labels.append(value_label)
            
            # 슬라이더
            slider = QtWidgets.QSlider(QtCore.Qt.Vertical)
            slider.setMinimum(5)
            slider.setMaximum(30)
            slider.setValue(int(self.thresholds[i] * 10))
            slider.setTickPosition(QtWidgets.QSlider.TicksRight)
            slider.setTickInterval(5)
            slider.valueChanged.connect(lambda value, idx=i: self.update_threshold(idx, value/10))
            sensor_frame.addWidget(slider)
            self.threshold_sliders.append(slider)
        
        # 그래프 레이아웃
        self.graph_layout = pg.GraphicsLayoutWidget()
        main_layout.addWidget(self.graph_layout, 1)  # 1은 stretch factor
        
        # 그래프 설정
        self.setup_plots()
    
    def setup_plots(self):
        # 8개 센서용 그래프 생성
        self.plots = []
        self.curves = []
        self.threshold_lines = []
        self.hit_regions = []
        
        # 2x4 그리드로 그래프 배치
        for i in range(8):
            row = i // 4
            col = i % 4
            
            # 그래프 생성
            plot = self.graph_layout.addPlot(row=row, col=col, title=f'센서 {i+1}')
            plot.setYRange(0, 3)
            plot.showGrid(x=True, y=True)
            
            # 데이터 곡선
            curve = plot.plot(pen=pg.mkPen('y', width=2))
            
            # 임계값 선
            threshold_line = pg.InfiniteLine(
                pos=self.thresholds[i], 
                angle=0, 
                pen=pg.mkPen('r', width=1, style=QtCore.Qt.DashLine),
                movable=False
            )
            plot.addItem(threshold_line)
            
            # 타격 표시용 영역
            hit_region = pg.LinearRegionItem(
                [0, MAX_DATA_POINTS], 
                brush=pg.mkBrush(0, 255, 0, 50),
                movable=False
            )
            hit_region.setVisible(False)
            plot.addItem(hit_region)
            
            self.plots.append(plot)
            self.curves.append(curve)
            self.threshold_lines.append(threshold_line)
            self.hit_regions.append(hit_region)
    
    def detect_serial_ports(self):
        """사용 가능한 시리얼 포트 감지"""
        self.port_combo.clear()
        ports = [port.device for port in serial.tools.list_ports.comports()]
        
        for port in ports:
            self.port_combo.addItem(port)
        
        # NodeMCU 자동 감지 (CH340 드라이버 사용)
        found_nodemcu = False
        for port in serial.tools.list_ports.comports():
            if 'CH340' in port.description or 'USB-SERIAL' in port.description:
                self.port_combo.setCurrentText(port.device)
                found_nodemcu = True
                break
        
        # 자동 감지 실패 시 첫 번째 포트 선택
        if not found_nodemcu and ports:
            self.port_combo.setCurrentIndex(0)
    
    def toggle_connection(self):
        """연결/연결 해제 토글"""
        if not self.is_running:
            self.connect()
        else:
            self.disconnect()
    
    def connect(self):
        """시리얼 포트 연결 및 데이터 수신 시작"""
        port = self.port_combo.currentText()
        if not port:
            QtWidgets.QMessageBox.critical(self, "오류", "시리얼 포트를 선택하세요.")
            return
        
        try:
            self.serial_port = serial.Serial(port, 115200, timeout=SERIAL_TIMEOUT)
            self.is_running = True
            self.connect_button.setText("연결 해제")
            
            # 데이터 수신 스레드 시작
            self.receive_thread = threading.Thread(target=self.receive_data)
            self.receive_thread.daemon = True
            self.receive_thread.start()
            
            QtWidgets.QMessageBox.information(self, "연결 성공", f"{port}에 연결되었습니다.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "연결 오류", f"연결 중 오류 발생: {str(e)}")
    
    def disconnect(self):
        """연결 해제"""
        self.is_running = False
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()
        self.connect_button.setText("연결")
    
    def receive_data(self):
        """시리얼 데이터 수신 스레드"""
        buffer = ""
        
        while self.is_running:
            try:
                if self.serial_port and self.serial_port.is_open:
                    data = self.serial_port.read(self.serial_port.in_waiting or 1)
                    if data:
                        buffer += data.decode('utf-8', errors='ignore')
                        
                        # 줄바꿈으로 데이터 분리
                        if '\n' in buffer:
                            lines = buffer.split('\n')
                            buffer = lines[-1]  # 마지막 불완전한 라인은 버퍼에 유지
                            
                            for line in lines[:-1]:
                                self.process_data(line.strip())
                
                time.sleep(0.001)  # CPU 사용량 감소
            except Exception as e:
                print(f"데이터 수신 오류: {str(e)}")
                time.sleep(1)
    
    def process_data(self, data_line):
        """수신된 데이터 처리"""
        try:
            # 데이터 형식: timestamp,sensor1,sensor2,...,sensor8
            parts = data_line.split(',')
            if len(parts) >= 9:  # 타임스탬프 + 8개 센서
                timestamp = float(parts[0]) / 1000.0  # ms를 초 단위로 변환
                
                # 센서 데이터 추출
                sensor_values = []
                for i in range(1, 9):
                    try:
                        value = float(parts[i])
                        sensor_values.append(value)
                    except ValueError:
                        sensor_values.append(0.0)
                
                # 데이터 큐에 추가
                self.data_queue.put((timestamp, sensor_values))
                
                # 타격 감지 및 키 입력 시뮬레이션
                self.detect_hits(sensor_values)
        except Exception as e:
            print(f"데이터 처리 오류: {str(e)}")
    
    def detect_hits(self, sensor_values):
        """타격 감지 및 키 입력 시뮬레이션"""
        current_time = time.time()
        
        for i, value in enumerate(sensor_values):
            # 임계값 초과 및 쿨다운 시간 확인
            if (value > self.thresholds[i] and 
                current_time - self.last_trigger_time[i] > COOLDOWN_TIME):
                
                # 타격 감지 표시
                self.hit_indicators[i] = True
                self.last_trigger_time[i] = current_time
                
                # 백그라운드 모드인 경우 키 입력 시뮬레이션
                if self.background_var.isChecked():
                    # 1-8 키 입력 시뮬레이션
                    key = str(i + 1)
                    try:
                        pyautogui.press(key)
                    except Exception as e:
                        print(f"키 입력 시뮬레이션 오류: {str(e)}")
    
    def update_threshold(self, sensor_idx, value):
        """임계값 업데이트"""
        self.thresholds[sensor_idx] = value
        self.threshold_labels[sensor_idx].setText(f"{value:.1f}")
        self.threshold_lines[sensor_idx].setValue(value)
    
    def update_plots(self):
        """그래프 업데이트"""
        # 큐에서 데이터 가져오기
        while not self.data_queue.empty():
            timestamp, values = self.data_queue.get()
            
            # 데이터 배열 업데이트 (순환 버퍼 방식)
            self.data_index = (self.data_index + 1) % MAX_DATA_POINTS
            self.timestamps[self.data_index] = timestamp
            
            for i, value in enumerate(values):
                self.sensor_data[i][self.data_index] = value
        
        # 그래프 업데이트
        x = np.arange(MAX_DATA_POINTS)
        for i in range(8):
            # 데이터 재정렬 (가장 최근 데이터가 오른쪽에 오도록)
            rolled_data = np.roll(self.sensor_data[i], -self.data_index-1)
            self.curves[i].setData(x, rolled_data)
            
            # 타격 표시 업데이트
            if self.hit_indicators[i]:
                self.hit_regions[i].setVisible(True)
                self.hit_indicators[i] = False
                
                # 0.2초 후 타격 표시 숨기기
                QtCore.QTimer.singleShot(200, lambda idx=i: self.hide_hit_indicator(idx))
    
    def hide_hit_indicator(self, sensor_idx):
        """타격 표시 숨기기"""
        self.hit_regions[sensor_idx].setVisible(False)
    
    def closeEvent(self, event):
        """프로그램 종료 처리"""
        self.is_running = False
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()
        event.accept()

if __name__ == "__main__":
    app = QtWidgets.QApplication(sys.argv)
    window = DrumSensorApp()
    window.show()
    sys.exit(app.exec_())
