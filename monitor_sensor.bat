@echo off
echo NodeMCU 센서 모니터링 시작...
echo COM13 포트에 연결 중...
echo 종료하려면 Ctrl+C를 누르세요.
echo.

:: 다른 프로세스가 포트를 사용 중인지 확인
tasklist /FI "IMAGENAME eq python.exe" | find "python.exe" > nul
if %ERRORLEVEL% EQU 0 (
    echo 다른 Python 프로세스가 실행 중입니다. 종료 중...
    taskkill /F /IM python.exe > nul 2>&1
    timeout /t 2 > nul
)

:: NodeMCU가 준비될 때까지 잠시 대기
timeout /t 2 > nul

:: 시리얼 모니터 연결
python -m serial.tools.miniterm COM13 115200
