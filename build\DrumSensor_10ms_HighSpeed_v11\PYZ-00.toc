('C:\\NEW2\\MPU6050_25ms_Backup\\build\\DrumSensor_10ms_HighSpeed_v11\\PYZ-00.pyz',
 [('OpenGL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\__init__.py',
   'PYMODULE'),
  ('OpenGL.GL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\__init__.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\__init__.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.ES2_compatibility',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\ES2_compatibility.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.ES3_compatibility',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\ES3_compatibility.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.arrays_of_arrays',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\arrays_of_arrays.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.base_instance',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\base_instance.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.clear_buffer_object',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\clear_buffer_object.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.compressed_texture_pixel_storage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\compressed_texture_pixel_storage.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.compute_shader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\compute_shader.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.conservative_depth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\conservative_depth.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.copy_image',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\copy_image.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.enhanced_layouts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\enhanced_layouts.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.explicit_uniform_location',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\explicit_uniform_location.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.fragment_layer_viewport',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\fragment_layer_viewport.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.framebuffer_no_attachments',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\framebuffer_no_attachments.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.get_program_binary',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\get_program_binary.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.imaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\imaging.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.internalformat_query',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\internalformat_query.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.internalformat_query2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\internalformat_query2.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.invalidate_subdata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\invalidate_subdata.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.map_buffer_alignment',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\map_buffer_alignment.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.multi_draw_indirect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\multi_draw_indirect.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.program_interface_query',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\program_interface_query.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.robust_buffer_access_behavior',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\robust_buffer_access_behavior.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.separate_shader_objects',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\separate_shader_objects.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.shader_atomic_counters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\shader_atomic_counters.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.shader_image_load_store',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\shader_image_load_store.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.shader_image_size',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\shader_image_size.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.shader_precision',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\shader_precision.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.shader_storage_buffer_object',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\shader_storage_buffer_object.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.shading_language_420pack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\shading_language_420pack.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.shading_language_packing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\shading_language_packing.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.stencil_texturing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\stencil_texturing.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.texture_buffer_object',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\texture_buffer_object.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.texture_buffer_range',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\texture_buffer_range.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.texture_query_levels',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\texture_query_levels.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.texture_storage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\texture_storage.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.texture_storage_multisample',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\texture_storage_multisample.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.texture_view',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\texture_view.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.transform_feedback_instanced',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\transform_feedback_instanced.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.uniform_buffer_object',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\uniform_buffer_object.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.vboimplementation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\vboimplementation.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.vertex_attrib_64bit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\vertex_attrib_64bit.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.vertex_attrib_binding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\vertex_attrib_binding.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.vertex_buffer_object',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\vertex_buffer_object.py',
   'PYMODULE'),
  ('OpenGL.GL.ARB.viewport_array',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\ARB\\viewport_array.py',
   'PYMODULE'),
  ('OpenGL.GL.KHR',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\KHR\\__init__.py',
   'PYMODULE'),
  ('OpenGL.GL.KHR.debug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\KHR\\debug.py',
   'PYMODULE'),
  ('OpenGL.GL.VERSION',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\VERSION\\__init__.py',
   'PYMODULE'),
  ('OpenGL.GL.VERSION.GL_1_0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\VERSION\\GL_1_0.py',
   'PYMODULE'),
  ('OpenGL.GL.VERSION.GL_1_1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\VERSION\\GL_1_1.py',
   'PYMODULE'),
  ('OpenGL.GL.VERSION.GL_1_2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\VERSION\\GL_1_2.py',
   'PYMODULE'),
  ('OpenGL.GL.VERSION.GL_1_2_images',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\VERSION\\GL_1_2_images.py',
   'PYMODULE'),
  ('OpenGL.GL.VERSION.GL_1_3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\VERSION\\GL_1_3.py',
   'PYMODULE'),
  ('OpenGL.GL.VERSION.GL_1_4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\VERSION\\GL_1_4.py',
   'PYMODULE'),
  ('OpenGL.GL.VERSION.GL_1_5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\VERSION\\GL_1_5.py',
   'PYMODULE'),
  ('OpenGL.GL.VERSION.GL_2_0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\VERSION\\GL_2_0.py',
   'PYMODULE'),
  ('OpenGL.GL.VERSION.GL_2_1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\VERSION\\GL_2_1.py',
   'PYMODULE'),
  ('OpenGL.GL.VERSION.GL_3_0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\VERSION\\GL_3_0.py',
   'PYMODULE'),
  ('OpenGL.GL.VERSION.GL_3_1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\VERSION\\GL_3_1.py',
   'PYMODULE'),
  ('OpenGL.GL.VERSION.GL_3_2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\VERSION\\GL_3_2.py',
   'PYMODULE'),
  ('OpenGL.GL.VERSION.GL_3_3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\VERSION\\GL_3_3.py',
   'PYMODULE'),
  ('OpenGL.GL.VERSION.GL_4_0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\VERSION\\GL_4_0.py',
   'PYMODULE'),
  ('OpenGL.GL.VERSION.GL_4_1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\VERSION\\GL_4_1.py',
   'PYMODULE'),
  ('OpenGL.GL.VERSION.GL_4_2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\VERSION\\GL_4_2.py',
   'PYMODULE'),
  ('OpenGL.GL.VERSION.GL_4_3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\VERSION\\GL_4_3.py',
   'PYMODULE'),
  ('OpenGL.GL.VERSION.GL_4_4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\VERSION\\GL_4_4.py',
   'PYMODULE'),
  ('OpenGL.GL.VERSION.GL_4_5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\VERSION\\GL_4_5.py',
   'PYMODULE'),
  ('OpenGL.GL.VERSION.GL_4_6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\VERSION\\GL_4_6.py',
   'PYMODULE'),
  ('OpenGL.GL.exceptional',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\exceptional.py',
   'PYMODULE'),
  ('OpenGL.GL.feedback',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\feedback.py',
   'PYMODULE'),
  ('OpenGL.GL.glget',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\glget.py',
   'PYMODULE'),
  ('OpenGL.GL.images',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\images.py',
   'PYMODULE'),
  ('OpenGL.GL.pointers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\pointers.py',
   'PYMODULE'),
  ('OpenGL.GL.selection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\selection.py',
   'PYMODULE'),
  ('OpenGL.GL.vboimplementation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GL\\vboimplementation.py',
   'PYMODULE'),
  ('OpenGL.GLU',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GLU\\__init__.py',
   'PYMODULE'),
  ('OpenGL.GLU.glunurbs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GLU\\glunurbs.py',
   'PYMODULE'),
  ('OpenGL.GLU.glustruct',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GLU\\glustruct.py',
   'PYMODULE'),
  ('OpenGL.GLU.projection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GLU\\projection.py',
   'PYMODULE'),
  ('OpenGL.GLU.quadrics',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GLU\\quadrics.py',
   'PYMODULE'),
  ('OpenGL.GLU.tess',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\GLU\\tess.py',
   'PYMODULE'),
  ('OpenGL._bytes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\_bytes.py',
   'PYMODULE'),
  ('OpenGL._configflags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\_configflags.py',
   'PYMODULE'),
  ('OpenGL._null',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\_null.py',
   'PYMODULE'),
  ('OpenGL._opaque',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\_opaque.py',
   'PYMODULE'),
  ('OpenGL.acceleratesupport',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\acceleratesupport.py',
   'PYMODULE'),
  ('OpenGL.arrays',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\arrays\\__init__.py',
   'PYMODULE'),
  ('OpenGL.arrays._arrayconstants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\arrays\\_arrayconstants.py',
   'PYMODULE'),
  ('OpenGL.arrays._buffers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\arrays\\_buffers.py',
   'PYMODULE'),
  ('OpenGL.arrays._strings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\arrays\\_strings.py',
   'PYMODULE'),
  ('OpenGL.arrays.arraydatatype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\arrays\\arraydatatype.py',
   'PYMODULE'),
  ('OpenGL.arrays.arrayhelpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\arrays\\arrayhelpers.py',
   'PYMODULE'),
  ('OpenGL.arrays.buffers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\arrays\\buffers.py',
   'PYMODULE'),
  ('OpenGL.arrays.ctypesarrays',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\arrays\\ctypesarrays.py',
   'PYMODULE'),
  ('OpenGL.arrays.ctypesparameters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\arrays\\ctypesparameters.py',
   'PYMODULE'),
  ('OpenGL.arrays.ctypespointers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\arrays\\ctypespointers.py',
   'PYMODULE'),
  ('OpenGL.arrays.formathandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\arrays\\formathandler.py',
   'PYMODULE'),
  ('OpenGL.arrays.lists',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\arrays\\lists.py',
   'PYMODULE'),
  ('OpenGL.arrays.nones',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\arrays\\nones.py',
   'PYMODULE'),
  ('OpenGL.arrays.numbers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\arrays\\numbers.py',
   'PYMODULE'),
  ('OpenGL.arrays.numpybuffers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\arrays\\numpybuffers.py',
   'PYMODULE'),
  ('OpenGL.arrays.numpymodule',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\arrays\\numpymodule.py',
   'PYMODULE'),
  ('OpenGL.arrays.strings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\arrays\\strings.py',
   'PYMODULE'),
  ('OpenGL.arrays.vbo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\arrays\\vbo.py',
   'PYMODULE'),
  ('OpenGL.constant',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\constant.py',
   'PYMODULE'),
  ('OpenGL.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\constants.py',
   'PYMODULE'),
  ('OpenGL.contextdata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\contextdata.py',
   'PYMODULE'),
  ('OpenGL.converters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\converters.py',
   'PYMODULE'),
  ('OpenGL.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\error.py',
   'PYMODULE'),
  ('OpenGL.extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\extensions.py',
   'PYMODULE'),
  ('OpenGL.images',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\images.py',
   'PYMODULE'),
  ('OpenGL.latebind',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\latebind.py',
   'PYMODULE'),
  ('OpenGL.lazywrapper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\lazywrapper.py',
   'PYMODULE'),
  ('OpenGL.logs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\logs.py',
   'PYMODULE'),
  ('OpenGL.platform',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\platform\\__init__.py',
   'PYMODULE'),
  ('OpenGL.platform.baseplatform',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\platform\\baseplatform.py',
   'PYMODULE'),
  ('OpenGL.platform.ctypesloader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\platform\\ctypesloader.py',
   'PYMODULE'),
  ('OpenGL.platform.win32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\platform\\win32.py',
   'PYMODULE'),
  ('OpenGL.plugins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\plugins.py',
   'PYMODULE'),
  ('OpenGL.raw',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\__init__.py',
   'PYMODULE'),
  ('OpenGL.raw.GL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\__init__.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\__init__.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.ES2_compatibility',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\ES2_compatibility.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.ES3_compatibility',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\ES3_compatibility.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.arrays_of_arrays',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\arrays_of_arrays.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.base_instance',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\base_instance.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.clear_buffer_object',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\clear_buffer_object.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.compressed_texture_pixel_storage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\compressed_texture_pixel_storage.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.compute_shader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\compute_shader.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.conservative_depth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\conservative_depth.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.copy_image',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\copy_image.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.enhanced_layouts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\enhanced_layouts.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.explicit_uniform_location',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\explicit_uniform_location.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.fragment_layer_viewport',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\fragment_layer_viewport.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.framebuffer_no_attachments',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\framebuffer_no_attachments.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.get_program_binary',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\get_program_binary.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.imaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\imaging.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.internalformat_query',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\internalformat_query.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.internalformat_query2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\internalformat_query2.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.invalidate_subdata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\invalidate_subdata.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.map_buffer_alignment',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\map_buffer_alignment.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.multi_draw_indirect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\multi_draw_indirect.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.program_interface_query',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\program_interface_query.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.robust_buffer_access_behavior',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\robust_buffer_access_behavior.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.separate_shader_objects',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\separate_shader_objects.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.shader_atomic_counters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\shader_atomic_counters.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.shader_image_load_store',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\shader_image_load_store.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.shader_image_size',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\shader_image_size.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.shader_objects',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\shader_objects.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.shader_precision',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\shader_precision.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.shader_storage_buffer_object',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\shader_storage_buffer_object.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.shading_language_420pack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\shading_language_420pack.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.shading_language_packing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\shading_language_packing.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.stencil_texturing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\stencil_texturing.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.texture_buffer_object',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\texture_buffer_object.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.texture_buffer_range',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\texture_buffer_range.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.texture_query_levels',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\texture_query_levels.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.texture_storage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\texture_storage.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.texture_storage_multisample',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\texture_storage_multisample.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.texture_view',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\texture_view.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.transform_feedback_instanced',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\transform_feedback_instanced.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.uniform_buffer_object',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\uniform_buffer_object.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.vertex_attrib_64bit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\vertex_attrib_64bit.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.vertex_attrib_binding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\vertex_attrib_binding.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.vertex_buffer_object',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\vertex_buffer_object.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.ARB.viewport_array',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\ARB\\viewport_array.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.KHR',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\KHR\\__init__.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.KHR.debug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\KHR\\debug.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.VERSION',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\VERSION\\__init__.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.VERSION.GL_1_0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\VERSION\\GL_1_0.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.VERSION.GL_1_1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\VERSION\\GL_1_1.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.VERSION.GL_1_2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\VERSION\\GL_1_2.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.VERSION.GL_1_3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\VERSION\\GL_1_3.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.VERSION.GL_1_4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\VERSION\\GL_1_4.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.VERSION.GL_1_5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\VERSION\\GL_1_5.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.VERSION.GL_2_0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\VERSION\\GL_2_0.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.VERSION.GL_2_1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\VERSION\\GL_2_1.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.VERSION.GL_3_0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\VERSION\\GL_3_0.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.VERSION.GL_3_1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\VERSION\\GL_3_1.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.VERSION.GL_3_2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\VERSION\\GL_3_2.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.VERSION.GL_3_3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\VERSION\\GL_3_3.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.VERSION.GL_4_0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\VERSION\\GL_4_0.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.VERSION.GL_4_1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\VERSION\\GL_4_1.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.VERSION.GL_4_2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\VERSION\\GL_4_2.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.VERSION.GL_4_3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\VERSION\\GL_4_3.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.VERSION.GL_4_4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\VERSION\\GL_4_4.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.VERSION.GL_4_5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\VERSION\\GL_4_5.py',
   'PYMODULE'),
  ('OpenGL.raw.GL.VERSION.GL_4_6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\VERSION\\GL_4_6.py',
   'PYMODULE'),
  ('OpenGL.raw.GL._errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\_errors.py',
   'PYMODULE'),
  ('OpenGL.raw.GL._glgets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\_glgets.py',
   'PYMODULE'),
  ('OpenGL.raw.GL._lookupint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\_lookupint.py',
   'PYMODULE'),
  ('OpenGL.raw.GL._types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GL\\_types.py',
   'PYMODULE'),
  ('OpenGL.raw.GLU',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GLU\\__init__.py',
   'PYMODULE'),
  ('OpenGL.raw.GLU.annotations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GLU\\annotations.py',
   'PYMODULE'),
  ('OpenGL.raw.GLU.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\raw\\GLU\\constants.py',
   'PYMODULE'),
  ('OpenGL.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\version.py',
   'PYMODULE'),
  ('OpenGL.wrapper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\OpenGL\\wrapper.py',
   'PYMODULE'),
  ('PIL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageGrab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageGrab.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyQt5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('PyQt5.uic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\uic\\__init__.py',
   'PYMODULE'),
  ('PyQt5.uic.Compiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\uic\\Compiler\\__init__.py',
   'PYMODULE'),
  ('PyQt5.uic.Compiler.compiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\uic\\Compiler\\compiler.py',
   'PYMODULE'),
  ('PyQt5.uic.Compiler.indenter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\uic\\Compiler\\indenter.py',
   'PYMODULE'),
  ('PyQt5.uic.Compiler.misc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\uic\\Compiler\\misc.py',
   'PYMODULE'),
  ('PyQt5.uic.Compiler.proxy_metaclass',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\uic\\Compiler\\proxy_metaclass.py',
   'PYMODULE'),
  ('PyQt5.uic.Compiler.qobjectcreator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\uic\\Compiler\\qobjectcreator.py',
   'PYMODULE'),
  ('PyQt5.uic.Compiler.qtproxies',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\uic\\Compiler\\qtproxies.py',
   'PYMODULE'),
  ('PyQt5.uic.Loader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\uic\\Loader\\__init__.py',
   'PYMODULE'),
  ('PyQt5.uic.Loader.loader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\uic\\Loader\\loader.py',
   'PYMODULE'),
  ('PyQt5.uic.Loader.qobjectcreator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\uic\\Loader\\qobjectcreator.py',
   'PYMODULE'),
  ('PyQt5.uic.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\uic\\exceptions.py',
   'PYMODULE'),
  ('PyQt5.uic.icon_cache',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\uic\\icon_cache.py',
   'PYMODULE'),
  ('PyQt5.uic.objcreator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\uic\\objcreator.py',
   'PYMODULE'),
  ('PyQt5.uic.port_v3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\uic\\port_v3\\__init__.py',
   'PYMODULE'),
  ('PyQt5.uic.port_v3.as_string',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\uic\\port_v3\\as_string.py',
   'PYMODULE'),
  ('PyQt5.uic.port_v3.ascii_upper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\uic\\port_v3\\ascii_upper.py',
   'PYMODULE'),
  ('PyQt5.uic.port_v3.proxy_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\uic\\port_v3\\proxy_base.py',
   'PYMODULE'),
  ('PyQt5.uic.port_v3.string_io',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\uic\\port_v3\\string_io.py',
   'PYMODULE'),
  ('PyQt5.uic.properties',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\uic\\properties.py',
   'PYMODULE'),
  ('PyQt5.uic.uiparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\uic\\uiparser.py',
   'PYMODULE'),
  ('__future__', 'C:\\Python313\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'C:\\Python313\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_colorize', 'C:\\Python313\\Lib\\_colorize.py', 'PYMODULE'),
  ('_compat_pickle', 'C:\\Python313\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'C:\\Python313\\Lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_ios_support', 'C:\\Python313\\Lib\\_ios_support.py', 'PYMODULE'),
  ('_opcode_metadata', 'C:\\Python313\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Python313\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'C:\\Python313\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Python313\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils._win32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\_win32.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils.tempfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\tempfile.py',
   'PYMODULE'),
  ('_pyrepl', 'C:\\Python313\\Lib\\_pyrepl\\__init__.py', 'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'C:\\Python313\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   'C:\\Python313\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.commands', 'C:\\Python313\\Lib\\_pyrepl\\commands.py', 'PYMODULE'),
  ('_pyrepl.completing_reader',
   'C:\\Python313\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('_pyrepl.console', 'C:\\Python313\\Lib\\_pyrepl\\console.py', 'PYMODULE'),
  ('_pyrepl.curses', 'C:\\Python313\\Lib\\_pyrepl\\curses.py', 'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'C:\\Python313\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   'C:\\Python313\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.input', 'C:\\Python313\\Lib\\_pyrepl\\input.py', 'PYMODULE'),
  ('_pyrepl.keymap', 'C:\\Python313\\Lib\\_pyrepl\\keymap.py', 'PYMODULE'),
  ('_pyrepl.main', 'C:\\Python313\\Lib\\_pyrepl\\main.py', 'PYMODULE'),
  ('_pyrepl.pager', 'C:\\Python313\\Lib\\_pyrepl\\pager.py', 'PYMODULE'),
  ('_pyrepl.reader', 'C:\\Python313\\Lib\\_pyrepl\\reader.py', 'PYMODULE'),
  ('_pyrepl.readline', 'C:\\Python313\\Lib\\_pyrepl\\readline.py', 'PYMODULE'),
  ('_pyrepl.simple_interact',
   'C:\\Python313\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.trace', 'C:\\Python313\\Lib\\_pyrepl\\trace.py', 'PYMODULE'),
  ('_pyrepl.types', 'C:\\Python313\\Lib\\_pyrepl\\types.py', 'PYMODULE'),
  ('_pyrepl.unix_console',
   'C:\\Python313\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'C:\\Python313\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.utils', 'C:\\Python313\\Lib\\_pyrepl\\utils.py', 'PYMODULE'),
  ('_pyrepl.windows_console',
   'C:\\Python313\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_sitebuiltins', 'C:\\Python313\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('_strptime', 'C:\\Python313\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'C:\\Python313\\Lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'C:\\Python313\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Python313\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'C:\\Python313\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events', 'C:\\Python313\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'C:\\Python313\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'C:\\Python313\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'C:\\Python313\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'C:\\Python313\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues', 'C:\\Python313\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'C:\\Python313\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'C:\\Python313\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams', 'C:\\Python313\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks', 'C:\\Python313\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'C:\\Python313\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.timeouts', 'C:\\Python313\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.transports',
   'C:\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock', 'C:\\Python313\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'C:\\Python313\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'C:\\Python313\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'C:\\Python313\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'C:\\Python313\\Lib\\bz2.py', 'PYMODULE'),
  ('cProfile', 'C:\\Python313\\Lib\\cProfile.py', 'PYMODULE'),
  ('calendar', 'C:\\Python313\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd', 'C:\\Python313\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'C:\\Python313\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Python313\\Lib\\codeop.py', 'PYMODULE'),
  ('colorsys', 'C:\\Python313\\Lib\\colorsys.py', 'PYMODULE'),
  ('concurrent', 'C:\\Python313\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'C:\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'C:\\Python313\\Lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'C:\\Python313\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'C:\\Python313\\Lib\\contextvars.py', 'PYMODULE'),
  ('contourpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\contourpy\\__init__.py',
   'PYMODULE'),
  ('contourpy._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\contourpy\\_version.py',
   'PYMODULE'),
  ('contourpy.array',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\contourpy\\array.py',
   'PYMODULE'),
  ('contourpy.chunk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\contourpy\\chunk.py',
   'PYMODULE'),
  ('contourpy.convert',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\contourpy\\convert.py',
   'PYMODULE'),
  ('contourpy.dechunk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\contourpy\\dechunk.py',
   'PYMODULE'),
  ('contourpy.enum_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\contourpy\\enum_util.py',
   'PYMODULE'),
  ('contourpy.typecheck',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\contourpy\\typecheck.py',
   'PYMODULE'),
  ('contourpy.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\contourpy\\types.py',
   'PYMODULE'),
  ('copy', 'C:\\Python313\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\Python313\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'C:\\Python313\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'C:\\Python313\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'C:\\Python313\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'C:\\Python313\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes', 'C:\\Python313\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('curses', 'C:\\Python313\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key', 'C:\\Python313\\Lib\\curses\\has_key.py', 'PYMODULE'),
  ('cycler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cycler\\__init__.py',
   'PYMODULE'),
  ('dataclasses', 'C:\\Python313\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'C:\\Python313\\Lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal', 'C:\\Python313\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'C:\\Python313\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'C:\\Python313\\Lib\\dis.py', 'PYMODULE'),
  ('doctest', 'C:\\Python313\\Lib\\doctest.py', 'PYMODULE'),
  ('email', 'C:\\Python313\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'C:\\Python313\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase',
   'C:\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime', 'C:\\Python313\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'C:\\Python313\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'C:\\Python313\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'C:\\Python313\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'C:\\Python313\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'C:\\Python313\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'C:\\Python313\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'C:\\Python313\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'C:\\Python313\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'C:\\Python313\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'C:\\Python313\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'C:\\Python313\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'C:\\Python313\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fileinput', 'C:\\Python313\\Lib\\fileinput.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Python313\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Python313\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\Python313\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\Python313\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Python313\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Python313\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'C:\\Python313\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'C:\\Python313\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Python313\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\Python313\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'C:\\Python313\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'C:\\Python313\\Lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'C:\\Python313\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'C:\\Python313\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'C:\\Python313\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.server', 'C:\\Python313\\Lib\\http\\server.py', 'PYMODULE'),
  ('importlib', 'C:\\Python313\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'C:\\Python313\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'C:\\Python313\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'C:\\Python313\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'C:\\Python313\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Python313\\Lib\\ipaddress.py', 'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('json', 'C:\\Python313\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'C:\\Python313\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'C:\\Python313\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\Python313\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('kiwisolver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\kiwisolver\\__init__.py',
   'PYMODULE'),
  ('kiwisolver.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\kiwisolver\\exceptions.py',
   'PYMODULE'),
  ('logging', 'C:\\Python313\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'C:\\Python313\\Lib\\lzma.py', 'PYMODULE'),
  ('matplotlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\__init__.py',
   'PYMODULE'),
  ('matplotlib._afm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_afm.py',
   'PYMODULE'),
  ('matplotlib._api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_api\\__init__.py',
   'PYMODULE'),
  ('matplotlib._api.deprecation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_api\\deprecation.py',
   'PYMODULE'),
  ('matplotlib._blocking_input',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_blocking_input.py',
   'PYMODULE'),
  ('matplotlib._cm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_cm.py',
   'PYMODULE'),
  ('matplotlib._cm_bivar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_cm_bivar.py',
   'PYMODULE'),
  ('matplotlib._cm_listed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_cm_listed.py',
   'PYMODULE'),
  ('matplotlib._cm_multivar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_cm_multivar.py',
   'PYMODULE'),
  ('matplotlib._color_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_color_data.py',
   'PYMODULE'),
  ('matplotlib._constrained_layout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_constrained_layout.py',
   'PYMODULE'),
  ('matplotlib._docstring',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_docstring.py',
   'PYMODULE'),
  ('matplotlib._enums',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_enums.py',
   'PYMODULE'),
  ('matplotlib._fontconfig_pattern',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_fontconfig_pattern.py',
   'PYMODULE'),
  ('matplotlib._layoutgrid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_layoutgrid.py',
   'PYMODULE'),
  ('matplotlib._mathtext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_mathtext.py',
   'PYMODULE'),
  ('matplotlib._mathtext_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_mathtext_data.py',
   'PYMODULE'),
  ('matplotlib._pylab_helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_pylab_helpers.py',
   'PYMODULE'),
  ('matplotlib._text_helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_text_helpers.py',
   'PYMODULE'),
  ('matplotlib._tight_bbox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_tight_bbox.py',
   'PYMODULE'),
  ('matplotlib._tight_layout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_tight_layout.py',
   'PYMODULE'),
  ('matplotlib._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_version.py',
   'PYMODULE'),
  ('matplotlib.artist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\artist.py',
   'PYMODULE'),
  ('matplotlib.axes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\axes\\__init__.py',
   'PYMODULE'),
  ('matplotlib.axes._axes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\axes\\_axes.py',
   'PYMODULE'),
  ('matplotlib.axes._base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\axes\\_base.py',
   'PYMODULE'),
  ('matplotlib.axes._secondary_axes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\axes\\_secondary_axes.py',
   'PYMODULE'),
  ('matplotlib.axis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\axis.py',
   'PYMODULE'),
  ('matplotlib.backend_bases',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backend_bases.py',
   'PYMODULE'),
  ('matplotlib.backend_managers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backend_managers.py',
   'PYMODULE'),
  ('matplotlib.backend_tools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backend_tools.py',
   'PYMODULE'),
  ('matplotlib.backends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backends\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_agg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backends\\backend_agg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_qt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backends\\backend_qt.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_qtagg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backends\\backend_qtagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backends\\backend_webagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg_core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backends\\backend_webagg_core.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backends\\qt_compat.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_editor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backends\\qt_editor\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_editor._formlayout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backends\\qt_editor\\_formlayout.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_editor.figureoptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backends\\qt_editor\\figureoptions.py',
   'PYMODULE'),
  ('matplotlib.backends.registry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backends\\registry.py',
   'PYMODULE'),
  ('matplotlib.bezier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\bezier.py',
   'PYMODULE'),
  ('matplotlib.category',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\category.py',
   'PYMODULE'),
  ('matplotlib.cbook',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\cbook.py',
   'PYMODULE'),
  ('matplotlib.cm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\cm.py',
   'PYMODULE'),
  ('matplotlib.collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\collections.py',
   'PYMODULE'),
  ('matplotlib.colorbar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\colorbar.py',
   'PYMODULE'),
  ('matplotlib.colorizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\colorizer.py',
   'PYMODULE'),
  ('matplotlib.colors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\colors.py',
   'PYMODULE'),
  ('matplotlib.container',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\container.py',
   'PYMODULE'),
  ('matplotlib.contour',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\contour.py',
   'PYMODULE'),
  ('matplotlib.dates',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\dates.py',
   'PYMODULE'),
  ('matplotlib.dviread',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\dviread.py',
   'PYMODULE'),
  ('matplotlib.figure',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\figure.py',
   'PYMODULE'),
  ('matplotlib.font_manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\font_manager.py',
   'PYMODULE'),
  ('matplotlib.gridspec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\gridspec.py',
   'PYMODULE'),
  ('matplotlib.hatch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\hatch.py',
   'PYMODULE'),
  ('matplotlib.image',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\image.py',
   'PYMODULE'),
  ('matplotlib.inset',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\inset.py',
   'PYMODULE'),
  ('matplotlib.layout_engine',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\layout_engine.py',
   'PYMODULE'),
  ('matplotlib.legend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\legend.py',
   'PYMODULE'),
  ('matplotlib.legend_handler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\legend_handler.py',
   'PYMODULE'),
  ('matplotlib.lines',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\lines.py',
   'PYMODULE'),
  ('matplotlib.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\markers.py',
   'PYMODULE'),
  ('matplotlib.mathtext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\mathtext.py',
   'PYMODULE'),
  ('matplotlib.mlab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\mlab.py',
   'PYMODULE'),
  ('matplotlib.offsetbox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\offsetbox.py',
   'PYMODULE'),
  ('matplotlib.patches',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\patches.py',
   'PYMODULE'),
  ('matplotlib.path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\path.py',
   'PYMODULE'),
  ('matplotlib.patheffects',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\patheffects.py',
   'PYMODULE'),
  ('matplotlib.projections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\projections\\__init__.py',
   'PYMODULE'),
  ('matplotlib.projections.geo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\projections\\geo.py',
   'PYMODULE'),
  ('matplotlib.projections.polar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\projections\\polar.py',
   'PYMODULE'),
  ('matplotlib.pyplot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\pyplot.py',
   'PYMODULE'),
  ('matplotlib.quiver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\quiver.py',
   'PYMODULE'),
  ('matplotlib.rcsetup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\rcsetup.py',
   'PYMODULE'),
  ('matplotlib.scale',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\scale.py',
   'PYMODULE'),
  ('matplotlib.spines',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\spines.py',
   'PYMODULE'),
  ('matplotlib.stackplot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\stackplot.py',
   'PYMODULE'),
  ('matplotlib.streamplot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\streamplot.py',
   'PYMODULE'),
  ('matplotlib.style',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\style\\__init__.py',
   'PYMODULE'),
  ('matplotlib.style.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\style\\core.py',
   'PYMODULE'),
  ('matplotlib.table',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\table.py',
   'PYMODULE'),
  ('matplotlib.texmanager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\texmanager.py',
   'PYMODULE'),
  ('matplotlib.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\text.py',
   'PYMODULE'),
  ('matplotlib.textpath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\textpath.py',
   'PYMODULE'),
  ('matplotlib.ticker',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\ticker.py',
   'PYMODULE'),
  ('matplotlib.transforms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\transforms.py',
   'PYMODULE'),
  ('matplotlib.tri',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\tri\\__init__.py',
   'PYMODULE'),
  ('matplotlib.tri._triangulation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\tri\\_triangulation.py',
   'PYMODULE'),
  ('matplotlib.tri._tricontour',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\tri\\_tricontour.py',
   'PYMODULE'),
  ('matplotlib.tri._trifinder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\tri\\_trifinder.py',
   'PYMODULE'),
  ('matplotlib.tri._triinterpolate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\tri\\_triinterpolate.py',
   'PYMODULE'),
  ('matplotlib.tri._tripcolor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\tri\\_tripcolor.py',
   'PYMODULE'),
  ('matplotlib.tri._triplot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\tri\\_triplot.py',
   'PYMODULE'),
  ('matplotlib.tri._trirefine',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\tri\\_trirefine.py',
   'PYMODULE'),
  ('matplotlib.tri._tritools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\tri\\_tritools.py',
   'PYMODULE'),
  ('matplotlib.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\typing.py',
   'PYMODULE'),
  ('matplotlib.units',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\units.py',
   'PYMODULE'),
  ('matplotlib.widgets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\widgets.py',
   'PYMODULE'),
  ('mimetypes', 'C:\\Python313\\Lib\\mimetypes.py', 'PYMODULE'),
  ('mouseinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\mouseinfo\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits', '-', 'PYMODULE'),
  ('mpl_toolkits.mplot3d',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\mpl_toolkits\\mplot3d\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.art3d',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\mpl_toolkits\\mplot3d\\art3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axes3d',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\mpl_toolkits\\mplot3d\\axes3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axis3d',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\mpl_toolkits\\mplot3d\\axis3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.proj3d',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\mpl_toolkits\\mplot3d\\proj3d.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'C:\\Python313\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Python313\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'C:\\Python313\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'C:\\Python313\\Lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'C:\\Python313\\Lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'C:\\Python313\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._abc', 'C:\\Python313\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('pathlib._local', 'C:\\Python313\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('pdb', 'C:\\Python313\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'C:\\Python313\\Lib\\pickle.py', 'PYMODULE'),
  ('pkg_resources',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'C:\\Python313\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'C:\\Python313\\Lib\\platform.py', 'PYMODULE'),
  ('plistlib', 'C:\\Python313\\Lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'C:\\Python313\\Lib\\pprint.py', 'PYMODULE'),
  ('profile', 'C:\\Python313\\Lib\\profile.py', 'PYMODULE'),
  ('pstats', 'C:\\Python313\\Lib\\pstats.py', 'PYMODULE'),
  ('py_compile', 'C:\\Python313\\Lib\\py_compile.py', 'PYMODULE'),
  ('pyautogui',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyautogui\\__init__.py',
   'PYMODULE'),
  ('pyautogui._pyautogui_osx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyautogui\\_pyautogui_osx.py',
   'PYMODULE'),
  ('pyautogui._pyautogui_win',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyautogui\\_pyautogui_win.py',
   'PYMODULE'),
  ('pyautogui._pyautogui_x11',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyautogui\\_pyautogui_x11.py',
   'PYMODULE'),
  ('pydoc', 'C:\\Python313\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'C:\\Python313\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pygetwindow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygetwindow\\__init__.py',
   'PYMODULE'),
  ('pygetwindow._pygetwindow_macos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygetwindow\\_pygetwindow_macos.py',
   'PYMODULE'),
  ('pygetwindow._pygetwindow_win',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygetwindow\\_pygetwindow_win.py',
   'PYMODULE'),
  ('pymsgbox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pymsgbox\\__init__.py',
   'PYMODULE'),
  ('pymsgbox._native_win',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pymsgbox\\_native_win.py',
   'PYMODULE'),
  ('pyparsing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyparsing.py',
   'PYMODULE'),
  ('pyperclip',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\GraphicsScene\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene.GraphicsScene',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\GraphicsScene\\GraphicsScene.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene.exportDialog',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\GraphicsScene\\exportDialog.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene.exportDialogTemplate_generic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\GraphicsScene\\exportDialogTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene.mouseEvents',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\GraphicsScene\\mouseEvents.py',
   'PYMODULE'),
  ('pyqtgraph.Point',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\Point.py',
   'PYMODULE'),
  ('pyqtgraph.Qt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\Qt\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.QtCore',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\Qt\\QtCore\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.QtGui',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\Qt\\QtGui\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.QtWidgets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\Qt\\QtWidgets\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\Qt\\compat\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.internals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\Qt\\internals.py',
   'PYMODULE'),
  ('pyqtgraph.SRTTransform',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\SRTTransform.py',
   'PYMODULE'),
  ('pyqtgraph.SRTTransform3D',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\SRTTransform3D.py',
   'PYMODULE'),
  ('pyqtgraph.SignalProxy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\SignalProxy.py',
   'PYMODULE'),
  ('pyqtgraph.ThreadsafeTimer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\ThreadsafeTimer.py',
   'PYMODULE'),
  ('pyqtgraph.Transform3D',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\Transform3D.py',
   'PYMODULE'),
  ('pyqtgraph.Vector',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\Vector.py',
   'PYMODULE'),
  ('pyqtgraph.WidgetGroup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\WidgetGroup.py',
   'PYMODULE'),
  ('pyqtgraph.canvas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\canvas\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.Canvas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\canvas\\Canvas.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.CanvasItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\canvas\\CanvasItem.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.CanvasManager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\canvas\\CanvasManager.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.CanvasTemplate_generic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\canvas\\CanvasTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.TransformGuiTemplate_generic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\canvas\\TransformGuiTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.colormap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\colormap.py',
   'PYMODULE'),
  ('pyqtgraph.colors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\colors\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.colors.palette',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\colors\\palette.py',
   'PYMODULE'),
  ('pyqtgraph.configfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\configfile.py',
   'PYMODULE'),
  ('pyqtgraph.console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\console\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.console.CmdInput',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\console\\CmdInput.py',
   'PYMODULE'),
  ('pyqtgraph.console.Console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\console\\Console.py',
   'PYMODULE'),
  ('pyqtgraph.console.exception_widget',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\console\\exception_widget.py',
   'PYMODULE'),
  ('pyqtgraph.console.repl_widget',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\console\\repl_widget.py',
   'PYMODULE'),
  ('pyqtgraph.console.stackwidget',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\console\\stackwidget.py',
   'PYMODULE'),
  ('pyqtgraph.debug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\debug.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\dockarea\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea.Container',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\dockarea\\Container.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea.Dock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\dockarea\\Dock.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea.DockArea',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\dockarea\\DockArea.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea.DockDrop',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\dockarea\\DockDrop.py',
   'PYMODULE'),
  ('pyqtgraph.exceptionHandling',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\exceptionHandling.py',
   'PYMODULE'),
  ('pyqtgraph.exporters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\exporters\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.CSVExporter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\exporters\\CSVExporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.Exporter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\exporters\\Exporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.HDF5Exporter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\exporters\\HDF5Exporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.ImageExporter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\exporters\\ImageExporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.Matplotlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\exporters\\Matplotlib.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.PrintExporter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\exporters\\PrintExporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.SVGExporter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\exporters\\SVGExporter.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\flowchart\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.Flowchart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\flowchart\\Flowchart.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.FlowchartCtrlTemplate_generic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\flowchart\\FlowchartCtrlTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.FlowchartGraphicsView',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\flowchart\\FlowchartGraphicsView.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.Node',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\flowchart\\Node.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.NodeLibrary',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\flowchart\\NodeLibrary.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.Terminal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\flowchart\\Terminal.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\flowchart\\library\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.Data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\flowchart\\library\\Data.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.Display',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\flowchart\\library\\Display.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.Filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\flowchart\\library\\Filters.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.Operators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\flowchart\\library\\Operators.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\flowchart\\library\\common.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\flowchart\\library\\functions.py',
   'PYMODULE'),
  ('pyqtgraph.functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\functions.py',
   'PYMODULE'),
  ('pyqtgraph.functions_numba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\functions_numba.py',
   'PYMODULE'),
  ('pyqtgraph.functions_qimage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\functions_qimage.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ArrowItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\ArrowItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.AxisItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\AxisItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.BarGraphItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\BarGraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ButtonItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\ButtonItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ColorBarItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\ColorBarItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.CurvePoint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\CurvePoint.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.DateAxisItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\DateAxisItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ErrorBarItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\ErrorBarItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.FillBetweenItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\FillBetweenItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GradientEditorItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\GradientEditorItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GradientLegend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\GradientLegend.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GradientPresets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\GradientPresets.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\GraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsLayout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsLayout.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsObject',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsObject.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsWidget',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsWidget.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsWidgetAnchor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsWidgetAnchor.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GridItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\GridItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.HistogramLUTItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\HistogramLUTItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ImageItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\ImageItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.InfiniteLine',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\InfiniteLine.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.IsocurveItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\IsocurveItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ItemGroup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\ItemGroup.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.LabelItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\LabelItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.LegendItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\LegendItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.LinearRegionItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\LinearRegionItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.MultiPlotItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\MultiPlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PColorMeshItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\PColorMeshItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotCurveItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\PlotCurveItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotDataItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\PlotDataItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotItem.PlotItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\PlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotItem.plotConfigTemplate_generic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\plotConfigTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ROI',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\ROI.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ScaleBar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\ScaleBar.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ScatterPlotItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\ScatterPlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.TargetItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\TargetItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.TextItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\TextItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.UIGraphicsItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\UIGraphicsItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.VTickGroup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\VTickGroup.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ViewBox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ViewBox.ViewBox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\ViewBox.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ViewBox.ViewBoxMenu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\ViewBoxMenu.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ViewBox.axisCtrlTemplate_generic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\axisCtrlTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.icons',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\icons\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.imageview',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\imageview\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.imageview.ImageView',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\imageview\\ImageView.py',
   'PYMODULE'),
  ('pyqtgraph.imageview.ImageViewTemplate_generic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\imageview\\ImageViewTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.metaarray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\metaarray\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.metaarray.MetaArray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\metaarray\\MetaArray.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\multiprocess\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess.bootstrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\multiprocess\\bootstrap.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess.parallelizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\multiprocess\\parallelizer.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess.processes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\multiprocess\\processes.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess.remoteproxy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\multiprocess\\remoteproxy.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.Parameter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\Parameter.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.ParameterItem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\ParameterItem.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.ParameterSystem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\ParameterSystem.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.ParameterTree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\ParameterTree.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.SystemSolver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\SystemSolver.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.interactive',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\interactive.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.action',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\action.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.actiongroup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\actiongroup.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.basetypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\basetypes.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.bool',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\bool.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.calendar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\calendar.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.checklist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\checklist.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.color',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\color.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.colormap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\colormap.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.colormaplut',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\colormaplut.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.file',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\file.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.font',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\font.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.list',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\list.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.numeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\numeric.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.pen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\pen.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.progress',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\progress.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.qtenum',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\qtenum.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.slider',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\slider.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.str',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\str.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\text.py',
   'PYMODULE'),
  ('pyqtgraph.reload',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\reload.py',
   'PYMODULE'),
  ('pyqtgraph.units',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\units.py',
   'PYMODULE'),
  ('pyqtgraph.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\util\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.util.colorama',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\util\\colorama\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.util.colorama.win32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\util\\colorama\\win32.py',
   'PYMODULE'),
  ('pyqtgraph.util.colorama.winterm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\util\\colorama\\winterm.py',
   'PYMODULE'),
  ('pyqtgraph.util.cprint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\util\\cprint.py',
   'PYMODULE'),
  ('pyqtgraph.util.cupy_helper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\util\\cupy_helper.py',
   'PYMODULE'),
  ('pyqtgraph.util.mutex',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\util\\mutex.py',
   'PYMODULE'),
  ('pyqtgraph.util.numba_helper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\util\\numba_helper.py',
   'PYMODULE'),
  ('pyqtgraph.widgets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.BusyCursor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\BusyCursor.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.CheckTable',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\CheckTable.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ColorButton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\ColorButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ColorMapButton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\ColorMapButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ColorMapMenu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\ColorMapMenu.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ColorMapWidget',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\ColorMapWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ComboBox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\ComboBox.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.DataFilterWidget',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\DataFilterWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.DataTreeWidget',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\DataTreeWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.DiffTreeWidget',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\DiffTreeWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.FeedbackButton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\FeedbackButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.FileDialog',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\FileDialog.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.GradientWidget',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\GradientWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.GraphicsLayoutWidget',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\GraphicsLayoutWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.GraphicsView',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\GraphicsView.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.GroupBox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\GroupBox.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.HistogramLUTWidget',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\HistogramLUTWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.JoystickButton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\JoystickButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.LayoutWidget',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\LayoutWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.MatplotlibWidget',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\MatplotlibWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.MultiPlotWidget',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\MultiPlotWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.PathButton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\PathButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.PenPreviewLabel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\PenPreviewLabel.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.PlotWidget',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\PlotWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ProgressDialog',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\ProgressDialog.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.RawImageWidget',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\RawImageWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.RemoteGraphicsView',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\RemoteGraphicsView.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ScatterPlotWidget',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\ScatterPlotWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.SpinBox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\SpinBox.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.TableWidget',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\TableWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.TreeWidget',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\TreeWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ValueLabel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\ValueLabel.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.VerticalLabel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyqtgraph\\widgets\\VerticalLabel.py',
   'PYMODULE'),
  ('pyrect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyrect\\__init__.py',
   'PYMODULE'),
  ('pyscreeze',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyscreeze\\__init__.py',
   'PYMODULE'),
  ('pytweening',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pytweening\\__init__.py',
   'PYMODULE'),
  ('queue', 'C:\\Python313\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Python313\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Python313\\Lib\\random.py', 'PYMODULE'),
  ('rlcompleter', 'C:\\Python313\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'C:\\Python313\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'C:\\Python313\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'C:\\Python313\\Lib\\selectors.py', 'PYMODULE'),
  ('serial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\serial\\__init__.py',
   'PYMODULE'),
  ('serial.serialcli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\serial\\serialcli.py',
   'PYMODULE'),
  ('serial.serialjava',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\serial\\serialjava.py',
   'PYMODULE'),
  ('serial.serialposix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\serial\\serialposix.py',
   'PYMODULE'),
  ('serial.serialutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\serial\\serialutil.py',
   'PYMODULE'),
  ('serial.serialwin32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\serial\\serialwin32.py',
   'PYMODULE'),
  ('serial.tools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\serial\\tools\\__init__.py',
   'PYMODULE'),
  ('serial.tools.list_ports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\serial\\tools\\list_ports.py',
   'PYMODULE'),
  ('serial.tools.list_ports_common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\serial\\tools\\list_ports_common.py',
   'PYMODULE'),
  ('serial.tools.list_ports_linux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\serial\\tools\\list_ports_linux.py',
   'PYMODULE'),
  ('serial.tools.list_ports_osx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\serial\\tools\\list_ports_osx.py',
   'PYMODULE'),
  ('serial.tools.list_ports_posix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\serial\\tools\\list_ports_posix.py',
   'PYMODULE'),
  ('serial.tools.list_ports_windows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\serial\\tools\\list_ports_windows.py',
   'PYMODULE'),
  ('serial.win32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\serial\\win32.py',
   'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'C:\\Python313\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\Python313\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Python313\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'C:\\Python313\\Lib\\site.py', 'PYMODULE'),
  ('six',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\six.py',
   'PYMODULE'),
  ('socket', 'C:\\Python313\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'C:\\Python313\\Lib\\socketserver.py', 'PYMODULE'),
  ('ssl', 'C:\\Python313\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'C:\\Python313\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'C:\\Python313\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'C:\\Python313\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'C:\\Python313\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'C:\\Python313\\Lib\\sysconfig\\__init__.py', 'PYMODULE'),
  ('tarfile', 'C:\\Python313\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Python313\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Python313\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Python313\\Lib\\threading.py', 'PYMODULE'),
  ('tkinter', 'C:\\Python313\\Lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.constants',
   'C:\\Python313\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.ttk', 'C:\\Python313\\Lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('token', 'C:\\Python313\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Python313\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib', 'C:\\Python313\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'C:\\Python313\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._re', 'C:\\Python313\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('tomllib._types', 'C:\\Python313\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\Python313\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'C:\\Python313\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'C:\\Python313\\Lib\\typing.py', 'PYMODULE'),
  ('unittest', 'C:\\Python313\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'C:\\Python313\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'C:\\Python313\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'C:\\Python313\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader', 'C:\\Python313\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.main', 'C:\\Python313\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.mock', 'C:\\Python313\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest.result', 'C:\\Python313\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.runner', 'C:\\Python313\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.signals', 'C:\\Python313\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.suite', 'C:\\Python313\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'C:\\Python313\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'C:\\Python313\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'C:\\Python313\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'C:\\Python313\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'C:\\Python313\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'C:\\Python313\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('uuid', 'C:\\Python313\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'C:\\Python313\\Lib\\webbrowser.py', 'PYMODULE'),
  ('xml', 'C:\\Python313\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom', 'C:\\Python313\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Python313\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg', 'C:\\Python313\\Lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Python313\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Python313\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom', 'C:\\Python313\\Lib\\xml\\dom\\minidom.py', 'PYMODULE'),
  ('xml.dom.pulldom', 'C:\\Python313\\Lib\\xml\\dom\\pulldom.py', 'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Python313\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree', 'C:\\Python313\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Python313\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Python313\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Python313\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Python313\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers', 'C:\\Python313\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax', 'C:\\Python313\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'C:\\Python313\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'C:\\Python313\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'C:\\Python313\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'C:\\Python313\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('yaml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile', 'C:\\Python313\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'C:\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'C:\\Python313\\Lib\\zipimport.py', 'PYMODULE')])
