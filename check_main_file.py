#!/usr/bin/env python3
"""
Check main.py file content
"""
import serial
import time

def check_main_file(port):
    try:
        print("main.py 파일 내용 확인...")
        ser = serial.Serial(port, 115200, timeout=10)
        time.sleep(2)
        
        # 프로그램 중단
        for i in range(3):
            ser.write(b'\x03')
            time.sleep(0.5)
        
        # 버퍼 클리어
        ser.read_all()
        
        # 파일 크기 확인
        print("파일 크기 확인...")
        ser.write(b'import os\r\n')
        time.sleep(1)
        ser.write(b'print("main.py size:", os.stat("main.py")[6])\r\n')
        time.sleep(2)
        response = ser.read_all().decode('utf-8', errors='ignore')
        print(f"크기 응답: {response}")
        
        # 파일 첫 몇 줄 읽기
        print("파일 첫 몇 줄 확인...")
        ser.write(b'with open("main.py") as f: lines = f.readlines()[:10]\r\n')
        time.sleep(1)
        ser.write(b'for i, line in enumerate(lines): print(f"{i+1}: {line.strip()}")\r\n')
        time.sleep(3)
        response = ser.read_all().decode('utf-8', errors='ignore')
        print(f"첫 줄들: {response}")
        
        # 직접 import 시도
        print("직접 import 시도...")
        ser.write(b'import main\r\n')
        time.sleep(5)
        response = ser.read_all().decode('utf-8', errors='ignore')
        print(f"Import 응답: {response}")
        
        # 센서 데이터 확인
        print("센서 데이터 확인 중...")
        start_time = time.time()
        while time.time() - start_time < 10:
            if ser.in_waiting > 0:
                try:
                    line = ser.readline().decode('utf-8', errors='ignore').strip()
                    if line and ',' in line:
                        print(f"데이터: {line}")
                except:
                    pass
            time.sleep(0.1)
        
        ser.close()
        
    except Exception as e:
        print(f"오류: {e}")

if __name__ == "__main__":
    check_main_file("COM14")
