#!/usr/bin/env python3
"""
Simple sensor connection test - one by one
"""
import serial
import time

def simple_sensor_test():
    try:
        print("간단한 센서 연결 테스트...")
        ser = serial.Serial("COM14", 115200, timeout=10)
        time.sleep(2)
        
        # 프로그램 중단
        for i in range(3):
            ser.write(b'\x03')
            time.sleep(0.5)
        
        ser.read_all()
        
        # I2C 초기화
        commands = [
            "from machine import I2C, Pin",
            "i2c = I2C(scl=Pin(5), sda=Pin(4), freq=100000)",
            "print('I2C ready for testing')"
        ]
        
        for cmd in commands:
            ser.write(f"{cmd}\r\n".encode('utf-8'))
            time.sleep(1)
        
        response = ser.read_all().decode('utf-8', errors='ignore')
        print(f"초기화: {response}")
        
        print("\n=== 각 채널별 상세 테스트 ===")
        
        # 각 채널 테스트
        for channel in range(8):
            print(f"\n--- 채널 {channel} 테스트 ---")
            
            test_commands = [
                f"print('Testing channel {channel}...')",
                f"try:",
                f"    # 채널 {channel} 선택",
                f"    i2c.writeto(0x70, bytes([{1 << channel}]))",
                f"    import time",
                f"    time.sleep_ms(100)",
                f"    # 스캔",
                f"    devices = i2c.scan()",
                f"    print(f'Channel {channel} all devices: {{[hex(d) for d in devices]}}')",
                f"    # MPU6050 확인",
                f"    if 0x68 in devices:",
                f"        print(f'✅ Channel {channel}: MPU6050 detected!')",
                f"        # WHO_AM_I 읽기 시도",
                f"        try:",
                f"            who_am_i = i2c.readfrom_mem(0x68, 0x75, 1)",
                f"            print(f'Channel {channel} WHO_AM_I: {{hex(who_am_i[0])}}')",
                f"            if who_am_i[0] == 0x68:",
                f"                print(f'✅ Channel {channel}: Valid MPU6050!')",
                f"            else:",
                f"                print(f'⚠️ Channel {channel}: Unexpected WHO_AM_I')",
                f"        except Exception as e:",
                f"            print(f'❌ Channel {channel}: WHO_AM_I read failed - {{e}}')",
                f"    else:",
                f"        print(f'❌ Channel {channel}: No MPU6050 found')",
                f"        if 0x70 in devices:",
                f"            print(f'   (TCA9548A still visible - channel switching may have failed)')",
                f"except Exception as e:",
                f"    print(f'Channel {channel} error: {{e}}')",
                f"print(f'Channel {channel} test completed')"
            ]
            
            for cmd in test_commands:
                ser.write(f"{cmd}\r\n".encode('utf-8'))
                time.sleep(0.1)
            
            # 결과 읽기
            time.sleep(3)
            response = ser.read_all().decode('utf-8', errors='ignore')
            if response.strip():
                lines = response.split('\n')
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('>>>') and not line.startswith('...'):
                        print(f"  {line}")
        
        # 요약
        print("\n=== 최종 요약 ===")
        summary_commands = [
            "print('=== FINAL SUMMARY ===')",
            "working_channels = []",
            "for ch in range(8):",
            "    try:",
            "        i2c.writeto(0x70, bytes([1 << ch]))",
            "        time.sleep_ms(50)",
            "        devices = i2c.scan()",
            "        if 0x68 in devices:",
            "            working_channels.append(ch)",
            "    except:",
            "        pass",
            "print(f'Working channels: {working_channels}')",
            "print(f'Total working sensors: {len(working_channels)}')",
            "print(f'Missing sensors: {8 - len(working_channels)}')"
        ]
        
        for cmd in summary_commands:
            ser.write(f"{cmd}\r\n".encode('utf-8'))
            time.sleep(0.2)
        
        time.sleep(2)
        response = ser.read_all().decode('utf-8', errors='ignore')
        if response.strip():
            lines = response.split('\n')
            for line in lines:
                line = line.strip()
                if line and not line.startswith('>>>') and not line.startswith('...'):
                    print(f"  {line}")
        
        ser.close()
        
    except Exception as e:
        print(f"오류: {e}")

if __name__ == "__main__":
    simple_sensor_test()
