#!/usr/bin/env python3
"""
Install MicroPython firmware on NodeMCU ESP8266
"""
import subprocess
import sys
import os
import requests

def download_firmware():
    """Download MicroPython firmware for ESP8266"""
    firmware_url = "https://micropython.org/resources/firmware/esp8266-20220618-v1.19.1.bin"
    firmware_file = "esp8266-micropython.bin"
    
    if os.path.exists(firmware_file):
        print(f"Firmware file {firmware_file} already exists")
        return firmware_file
    
    print("Downloading MicroPython firmware...")
    try:
        response = requests.get(firmware_url)
        response.raise_for_status()
        
        with open(firmware_file, 'wb') as f:
            f.write(response.content)
        
        print(f"Downloaded {firmware_file}")
        return firmware_file
    except Exception as e:
        print(f"Error downloading firmware: {e}")
        return None

def flash_firmware(port, firmware_file):
    """Flash MicroPython firmware to ESP8266"""
    try:
        print("Erasing flash...")
        subprocess.run([
            sys.executable, "-m", "esptool",
            "--port", port,
            "erase_flash"
        ], check=True)
        
        print("Flashing MicroPython firmware...")
        subprocess.run([
            sys.executable, "-m", "esptool",
            "--port", port,
            "--baud", "460800",
            "write_flash",
            "--flash_size=detect",
            "0",
            firmware_file
        ], check=True)
        
        print("MicroPython firmware installed successfully!")
        print("Please reset your NodeMCU and try connecting again.")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"Error flashing firmware: {e}")
        return False

if __name__ == "__main__":
    port = "COM14"
    
    print("This will install MicroPython firmware on your NodeMCU.")
    print("WARNING: This will erase all existing data on the device!")
    
    confirm = input("Do you want to continue? (y/N): ")
    if confirm.lower() != 'y':
        print("Operation cancelled.")
        sys.exit(0)
    
    firmware_file = download_firmware()
    if firmware_file:
        if flash_firmware(port, firmware_file):
            print("\nFirmware installation completed!")
            print("You can now use Thonny IDE to upload your Python files.")
        else:
            print("Firmware installation failed!")
    else:
        print("Could not download firmware!")
