#!/usr/bin/env python3
"""
I2C speed optimization test for 8 sensors
Find maximum stable communication speed
"""
import serial
import time

def test_i2c_speed_optimization():
    try:
        print("=== I2C 통신 속도 최적화 테스트 ===")
        print("8개 센서 모두 연결된 상태에서 최대 안정 속도 찾기")
        ser = serial.Serial("COM14", 115200, timeout=20)
        time.sleep(2)
        
        # 프로그램 중단
        for i in range(3):
            ser.write(b'\x03')
            time.sleep(0.5)
        
        ser.read_all()
        
        # 테스트할 주파수들 (낮은 것부터 높은 것까지)
        frequencies = [
            10000,   # 10kHz
            20000,   # 20kHz
            50000,   # 50kHz
            100000,  # 100kHz (표준)
            200000,  # 200kHz
            400000,  # 400kHz (고속)
            800000,  # 800kHz (초고속)
            1000000, # 1MHz (최고속)
        ]
        
        results = {}
        
        print("\n각 주파수별 8개 센서 감지 테스트...")
        
        for freq in frequencies:
            print(f"\n{'='*60}")
            print(f"🔍 {freq}Hz ({freq/1000:.0f}kHz) 테스트")
            print(f"{'='*60}")
            
            try:
                # I2C 초기화
                init_commands = [
                    "from machine import I2C, Pin",
                    f"i2c = I2C(scl=Pin(5), sda=Pin(4), freq={freq})",
                    f"print('I2C initialized at {freq}Hz')",
                    "import time"
                ]
                
                for cmd in init_commands:
                    ser.write(f"{cmd}\r\n".encode('utf-8'))
                    time.sleep(0.3)
                
                response = ser.read_all().decode('utf-8', errors='ignore')
                print(f"초기화: {response.strip()}")
                
                # 전체 I2C 스캔
                ser.write(b'all_devices = i2c.scan()\r\n')
                time.sleep(1)
                ser.write(b'print("All devices:", [hex(d) for d in all_devices])\r\n')
                time.sleep(1)
                
                response = ser.read_all().decode('utf-8', errors='ignore')
                print(f"전체 스캔: {response.strip()}")
                
                # TCA9548A 확인
                ser.write(b'tca_ok = 0x70 in all_devices\r\n')
                ser.write(b'print("TCA9548A:", "OK" if tca_ok else "FAIL")\r\n')
                time.sleep(1)
                
                response = ser.read_all().decode('utf-8', errors='ignore')
                print(f"TCA9548A: {response.strip()}")
                
                # 각 채널별 센서 확인
                print("8개 채널 센서 확인 중...")
                
                channel_test_commands = [
                    "sensor_results = []",
                    "error_count = 0",
                    "for ch in range(8):",
                    "    try:",
                    "        i2c.writeto(0x70, bytes([1 << ch]))",
                    "        time.sleep_ms(50)",
                    "        ch_devices = i2c.scan()",
                    "        if 0x68 in ch_devices:",
                    "            sensor_results.append(ch)",
                    "            print(f'✅ Channel {ch}: MPU6050 OK')",
                    "        else:",
                    "            print(f'❌ Channel {ch}: No MPU6050')",
                    "    except Exception as e:",
                    "        error_count += 1",
                    "        print(f'💥 Channel {ch}: Error - {e}')",
                    f"print(f'📊 {freq}Hz Results:')",
                    "print(f'  Detected sensors: {len(sensor_results)}/8')",
                    "print(f'  Active channels: {sensor_results}')",
                    "print(f'  Communication errors: {error_count}')",
                    f"success_rate = (8 - error_count) / 8 * 100",
                    f"print(f'  Success rate: {{success_rate:.1f}}%')"
                ]
                
                for cmd in channel_test_commands:
                    ser.write(f"{cmd}\r\n".encode('utf-8'))
                    time.sleep(0.1)
                
                # 결과 읽기
                time.sleep(5)
                response = ser.read_all().decode('utf-8', errors='ignore')
                
                # 결과 파싱
                lines = response.split('\n')
                sensor_count = 0
                error_count = 0
                success_rate = 0
                detected_channels = []
                
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('>>>') and not line.startswith('...'):
                        print(f"  {line}")
                        
                        if "Detected sensors:" in line:
                            try:
                                sensor_count = int(line.split(":")[1].split("/")[0].strip())
                            except:
                                pass
                        elif "Communication errors:" in line:
                            try:
                                error_count = int(line.split(":")[1].strip())
                            except:
                                pass
                        elif "Success rate:" in line:
                            try:
                                success_rate = float(line.split(":")[1].replace("%", "").strip())
                            except:
                                pass
                        elif "Active channels:" in line:
                            try:
                                channels_str = line.split(":")[1].strip()
                                import re
                                numbers = re.findall(r'\d+', channels_str)
                                detected_channels = [int(n) for n in numbers]
                            except:
                                pass
                
                # 결과 저장
                results[freq] = {
                    'sensor_count': sensor_count,
                    'error_count': error_count,
                    'success_rate': success_rate,
                    'channels': detected_channels
                }
                
                print(f"\n📈 {freq}Hz 요약:")
                print(f"   감지된 센서: {sensor_count}/8개")
                print(f"   통신 오류: {error_count}개")
                print(f"   성공률: {success_rate:.1f}%")
                print(f"   활성 채널: {detected_channels}")
                
                # 성능 평가
                if sensor_count == 8 and error_count == 0:
                    print(f"🏆 {freq}Hz: 완벽한 성능!")
                elif sensor_count >= 6 and success_rate >= 90:
                    print(f"✅ {freq}Hz: 우수한 성능")
                elif sensor_count >= 4 and success_rate >= 70:
                    print(f"⚠️ {freq}Hz: 보통 성능")
                else:
                    print(f"❌ {freq}Hz: 성능 부족")
                
            except Exception as e:
                print(f"❌ {freq}Hz 테스트 실패: {e}")
                results[freq] = {
                    'sensor_count': 0,
                    'error_count': 8,
                    'success_rate': 0,
                    'channels': []
                }
            
            time.sleep(2)
        
        # 최종 분석
        print(f"\n{'='*60}")
        print(f"🏆 최종 성능 분석")
        print(f"{'='*60}")
        
        best_freq = 0
        best_score = 0
        
        for freq, result in results.items():
            score = result['sensor_count'] * 10 + result['success_rate']
            print(f"{freq:>8}Hz ({freq/1000:>4.0f}kHz): {result['sensor_count']}/8 센서, "
                  f"{result['success_rate']:>5.1f}% 성공률, 점수: {score:>6.1f}")
            
            if score > best_score:
                best_score = score
                best_freq = freq
        
        print(f"\n🎯 권장 설정:")
        if best_freq > 0:
            print(f"   최적 주파수: {best_freq}Hz ({best_freq/1000:.0f}kHz)")
            print(f"   예상 성능: {results[best_freq]['sensor_count']}/8 센서")
            print(f"   성공률: {results[best_freq]['success_rate']:.1f}%")
        else:
            print("   적절한 주파수를 찾지 못했습니다.")
        
        # 안정성 기준 권장
        stable_freqs = [freq for freq, result in results.items() 
                       if result['sensor_count'] == 8 and result['error_count'] == 0]
        
        if stable_freqs:
            max_stable = max(stable_freqs)
            print(f"\n🛡️ 최대 안정 주파수: {max_stable}Hz ({max_stable/1000:.0f}kHz)")
            print(f"   (8개 센서 모두 오류 없이 감지)")
        
        ser.close()
        
    except Exception as e:
        print(f"오류: {e}")

if __name__ == "__main__":
    test_i2c_speed_optimization()
