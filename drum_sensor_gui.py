"""
드럼 센서 모니터링 및 키 입력 시뮬레이션 프로그램

이 프로그램은 NodeMCU에서 전송하는 8개 센서 데이터를 그래프로 표시하고,
임계값을 초과하는 타격을 감지하여 키보드 입력을 시뮬레이션합니다.
"""
import sys
import time
import threading
import queue
import serial
import serial.tools.list_ports
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import tkinter as tk
from tkinter import ttk, messagebox
import pyautogui  # 키보드 입력 시뮬레이션용

# 상수 정의
MAX_DATA_POINTS = 100  # 그래프에 표시할 최대 데이터 포인트 수
DEFAULT_THRESHOLD = 1.2  # 기본 임계값 (g)
SERIAL_TIMEOUT = 0.1  # 시리얼 타임아웃 (초)
COOLDOWN_TIME = 0.1  # 타격 감지 후 쿨다운 시간 (초)

class DrumSensorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("드럼 센서 모니터링")
        self.root.geometry("1200x800")
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 변수 초기화
        self.serial_port = None
        self.is_running = False
        self.data_queue = queue.Queue()
        self.sensor_data = [[] for _ in range(8)]  # 8개 센서 데이터 저장
        self.timestamps = []  # 타임스탬프 저장
        self.thresholds = [DEFAULT_THRESHOLD] * 8  # 8개 센서 임계값
        self.last_trigger_time = [0] * 8  # 마지막 트리거 시간
        self.hit_indicators = [False] * 8  # 타격 표시

        # UI 구성
        self.create_widgets()

        # 그래프 초기화
        self.setup_plots()

        # 시리얼 포트 자동 감지
        self.detect_serial_ports()

    def create_widgets(self):
        # 프레임 구성
        control_frame = ttk.Frame(self.root, padding="10")
        control_frame.pack(fill=tk.X)

        # 시리얼 포트 선택
        ttk.Label(control_frame, text="시리얼 포트:").pack(side=tk.LEFT, padx=5)
        self.port_combo = ttk.Combobox(control_frame, width=15)
        self.port_combo.pack(side=tk.LEFT, padx=5)

        # 새로고침 버튼
        ttk.Button(control_frame, text="새로고침", command=self.detect_serial_ports).pack(side=tk.LEFT, padx=5)

        # 연결/연결 해제 버튼
        self.connect_button = ttk.Button(control_frame, text="연결", command=self.toggle_connection)
        self.connect_button.pack(side=tk.LEFT, padx=5)

        # 백그라운드 모드 체크박스
        self.background_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(control_frame, text="백그라운드 모드", variable=self.background_var).pack(side=tk.LEFT, padx=5)

        # 임계값 조절 프레임
        threshold_frame = ttk.LabelFrame(self.root, text="임계값 조절", padding="10")
        threshold_frame.pack(fill=tk.X, padx=10, pady=5)

        # 각 센서별 임계값 슬라이더
        self.threshold_sliders = []
        for i in range(8):
            sensor_frame = ttk.Frame(threshold_frame)
            sensor_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

            ttk.Label(sensor_frame, text=f"센서 {i+1}").pack(anchor=tk.W)

            slider = ttk.Scale(sensor_frame, from_=0.5, to=3.0, orient=tk.HORIZONTAL,
                              value=self.thresholds[i], length=100)
            slider.pack(fill=tk.X)
            slider.bind("<ButtonRelease-1>", lambda event, idx=i: self.update_threshold(idx))

            value_label = ttk.Label(sensor_frame, text=f"{self.thresholds[i]:.1f}")
            value_label.pack()

            self.threshold_sliders.append((slider, value_label))

        # 그래프 프레임
        self.graph_frame = ttk.Frame(self.root)
        self.graph_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

    def setup_plots(self):
        # 그래프 설정
        self.fig, self.axes = plt.subplots(2, 4, figsize=(12, 6))
        self.axes = self.axes.flatten()

        # 각 서브플롯 초기화
        self.lines = []
        self.threshold_lines = []
        self.hit_patches = []

        for i, ax in enumerate(self.axes):
            line, = ax.plot([], [], 'b-', lw=2)
            threshold_line, = ax.plot([], [], 'r--', lw=1)
            hit_patch = plt.Rectangle((0, 0), 1, 1, color='green', alpha=0.3, visible=False)
            ax.add_patch(hit_patch)

            ax.set_ylim(0, 3)
            ax.set_xlim(0, MAX_DATA_POINTS)
            ax.set_title(f'센서 {i+1}')
            ax.set_xlabel('시간')
            ax.set_ylabel('가속도 (g)')
            ax.grid(True)

            self.lines.append(line)
            self.threshold_lines.append(threshold_line)
            self.hit_patches.append(hit_patch)

        self.fig.tight_layout()

        # Tkinter에 그래프 추가
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.graph_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 애니메이션 설정
        self.ani = FuncAnimation(self.fig, self.update_plot, interval=50, blit=False)

    def detect_serial_ports(self):
        """사용 가능한 시리얼 포트 감지"""
        ports = [port.device for port in serial.tools.list_ports.comports()]
        self.port_combo['values'] = ports

        # NodeMCU 자동 감지 (CH340 드라이버 사용)
        found_nodemcu = False
        for port in serial.tools.list_ports.comports():
            if 'CH340' in port.description or 'USB-SERIAL' in port.description:
                self.port_combo.set(port.device)
                found_nodemcu = True
                break

        # 자동 감지 실패 시 첫 번째 포트 선택
        if not found_nodemcu and ports:
            self.port_combo.set(ports[0])

    def toggle_connection(self):
        """연결/연결 해제 토글"""
        if not self.is_running:
            self.connect()
        else:
            self.disconnect()

    def connect(self):
        """시리얼 포트 연결 및 데이터 수신 시작"""
        port = self.port_combo.get()
        if not port:
            messagebox.showerror("오류", "시리얼 포트를 선택하세요.")
            return

        try:
            self.serial_port = serial.Serial(port, 115200, timeout=SERIAL_TIMEOUT)
            self.is_running = True
            self.connect_button.config(text="연결 해제")

            # 데이터 수신 스레드 시작
            self.receive_thread = threading.Thread(target=self.receive_data)
            self.receive_thread.daemon = True
            self.receive_thread.start()

            messagebox.showinfo("연결 성공", f"{port}에 연결되었습니다.")
        except Exception as e:
            messagebox.showerror("연결 오류", f"연결 중 오류 발생: {str(e)}")

    def disconnect(self):
        """연결 해제"""
        self.is_running = False
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()
        self.connect_button.config(text="연결")

    def receive_data(self):
        """시리얼 데이터 수신 스레드"""
        buffer = ""

        while self.is_running:
            try:
                if self.serial_port and self.serial_port.is_open:
                    data = self.serial_port.read(self.serial_port.in_waiting or 1)
                    if data:
                        buffer += data.decode('utf-8', errors='ignore')

                        # 줄바꿈으로 데이터 분리
                        if '\n' in buffer:
                            lines = buffer.split('\n')
                            buffer = lines[-1]  # 마지막 불완전한 라인은 버퍼에 유지

                            for line in lines[:-1]:
                                self.process_data(line.strip())

                time.sleep(0.01)  # CPU 사용량 감소
            except Exception as e:
                print(f"데이터 수신 오류: {str(e)}")
                time.sleep(1)

    def process_data(self, data_line):
        """수신된 데이터 처리"""
        try:
            # 데이터 형식: timestamp,sensor1,sensor2,...,sensor8
            parts = data_line.split(',')
            if len(parts) >= 9:  # 타임스탬프 + 8개 센서
                timestamp = float(parts[0]) / 1000.0  # ms를 초 단위로 변환

                # 센서 데이터 추출
                sensor_values = []
                for i in range(1, 9):
                    try:
                        value = float(parts[i])
                        sensor_values.append(value)
                    except ValueError:
                        sensor_values.append(0.0)

                # 데이터 큐에 추가
                self.data_queue.put((timestamp, sensor_values))

                # 타격 감지 및 키 입력 시뮬레이션
                self.detect_hits(sensor_values)
        except Exception as e:
            print(f"데이터 처리 오류: {str(e)}")

    def detect_hits(self, sensor_values):
        """타격 감지 및 키 입력 시뮬레이션"""
        current_time = time.time()

        for i, value in enumerate(sensor_values):
            # 임계값 초과 및 쿨다운 시간 확인
            if (value > self.thresholds[i] and
                current_time - self.last_trigger_time[i] > COOLDOWN_TIME):

                # 타격 감지 표시
                self.hit_indicators[i] = True
                self.last_trigger_time[i] = current_time

                # 백그라운드 모드인 경우 키 입력 시뮬레이션
                if self.background_var.get():
                    # 1-8 키 입력 시뮬레이션
                    key = str(i + 1)
                    try:
                        pyautogui.press(key)
                    except Exception as e:
                        print(f"키 입력 시뮬레이션 오류: {str(e)}")

    def update_threshold(self, sensor_idx):
        """임계값 업데이트"""
        slider, label = self.threshold_sliders[sensor_idx]
        value = slider.get()
        self.thresholds[sensor_idx] = value
        label.config(text=f"{value:.1f}")

    def update_plot(self, frame):
        """그래프 업데이트"""
        # 큐에서 데이터 가져오기
        while not self.data_queue.empty():
            timestamp, values = self.data_queue.get()

            self.timestamps.append(timestamp)
            for i, value in enumerate(values):
                self.sensor_data[i].append(value)

            # 데이터 길이 제한
            if len(self.timestamps) > MAX_DATA_POINTS:
                self.timestamps.pop(0)
                for i in range(8):
                    self.sensor_data[i].pop(0)

        # 그래프 업데이트
        x = np.arange(len(self.timestamps))
        for i in range(8):
            if self.sensor_data[i]:
                self.lines[i].set_data(x, self.sensor_data[i])

                # 임계값 선 업데이트
                self.threshold_lines[i].set_data([0, MAX_DATA_POINTS],
                                               [self.thresholds[i], self.thresholds[i]])

                # 타격 표시 업데이트
                if self.hit_indicators[i]:
                    self.hit_patches[i].set_visible(True)
                    self.hit_indicators[i] = False  # 표시 초기화

                    # 0.2초 후 타격 표시 숨기기
                    self.root.after(200, lambda idx=i: self.hide_hit_indicator(idx))

        return self.lines + self.threshold_lines + self.hit_patches

    def hide_hit_indicator(self, sensor_idx):
        """타격 표시 숨기기"""
        self.hit_patches[sensor_idx].set_visible(False)

    def on_closing(self):
        """프로그램 종료 처리"""
        self.is_running = False
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()
        self.root.destroy()
        sys.exit(0)

if __name__ == "__main__":
    root = tk.Tk()
    app = DrumSensorApp(root)
    root.mainloop()
