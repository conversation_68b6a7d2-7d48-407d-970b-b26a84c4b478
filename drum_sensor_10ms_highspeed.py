"""
드럼 센서 모니터링 및 키 입력 시뮬레이션 프로그램 (10ms 고속 버전)

이 프로그램은 NodeMCU에서 전송하는 8개 센서 데이터를 10ms 간격으로 수신하여
실시간 그래프로 표시하고, 임계값을 초과하는 타격을 감지하여 키보드 입력을 시뮬레이션합니다.
PyQtGraph를 사용하여 하드웨어 가속을 지원하며, 100Hz 고속 데이터에 최적화되었습니다.
"""
import sys
import os
import time
import threading
import queue
import numpy as np
import serial
import serial.tools.list_ports
import pyautogui  # 키보드 입력 시뮬레이션용
import json  # 설정 저장 및 불러오기용

# PyQtGraph 설정
import pyqtgraph as pg
from pyqtgraph.Qt import QtCore, QtGui, QtWidgets

# 상수 정의 - 10ms 고속 모드에 최적화
MAX_DATA_POINTS = 200  # 그래프에 표시할 최대 데이터 포인트 수 (2초분)
DEFAULT_THRESHOLD = 1.5  # 기본 임계값 (g) - 약간 증가
SERIAL_TIMEOUT = 0.01  # 시리얼 타임아웃 (10ms로 단축)
COOLDOWN_TIME = 0.05  # 타격 감지 후 쿨다운 시간 (50ms로 단축)
UPDATE_INTERVAL = 5  # 그래프 업데이트 간격 (5ms로 단축)
DEFAULT_KEY_PRESS_TIME = 0.1  # 기본 키 누름 시간 (100ms로 단축)
MIN_KEY_PRESS_TIME = 0.05  # 최소 키 누름 시간 (50ms)
MAX_KEY_PRESS_TIME = 0.3  # 최대 키 누름 시간 (300ms)

# 키 매핑 설정 (센서 인덱스 -> 키) - 드럼 패드 레이아웃에 최적화
KEY_MAPPING = ['a', 's', 'd', 'f', 'j', 'k', 'l', ';']

# 하드웨어 가속 활성화
pg.setConfigOptions(antialias=True, useOpenGL=True)

class HighSpeedDrumSensorApp(QtWidgets.QMainWindow):
    def __init__(self):
        super().__init__()

        # 설정 파일 경로
        try:
            if getattr(sys, 'frozen', False):
                application_path = os.path.dirname(sys.executable)
            else:
                application_path = os.path.dirname(os.path.abspath(__file__))

            self.config_file = os.path.join(application_path, 'drum_sensor_10ms_config.json')
            print(f"설정 파일 경로: {self.config_file}")
        except Exception as e:
            print(f"설정 파일 경로 설정 오류: {str(e)}")
            self.config_file = 'drum_sensor_10ms_config.json'

        # 변수 초기화 - 고속 모드에 최적화
        self.serial_port = None
        self.is_running = False
        self.data_queue = queue.Queue(maxsize=1000)  # 큐 크기 증가
        self.sensor_data = [np.zeros(MAX_DATA_POINTS) for _ in range(8)]
        self.sample_indices = np.arange(MAX_DATA_POINTS)
        self.thresholds = [DEFAULT_THRESHOLD] * 8
        self.last_trigger_time = [0] * 8
        self.hit_indicators = [False] * 8
        self.data_index = 0
        self.sample_count = 0
        self.background_mode = True
        self.key_press_time = DEFAULT_KEY_PRESS_TIME

        # 성능 모니터링 변수
        self.last_data_time = time.time()
        self.data_rate_counter = 0
        self.actual_data_rate = 0.0

        # 고성능 병렬 키 입력 시스템
        self.key_input_queue = queue.Queue(maxsize=200)  # 큐 크기 증가
        self.key_input_threads = []  # 다중 스레드 풀
        self.key_input_running = False
        self.max_key_threads = 8  # 최대 동시 키 입력 스레드 수
        self.active_keys = set()  # 현재 활성 키 추적
        self.key_lock = threading.Lock()  # 키 상태 동기화용

        # 동적 Y축 범위 관리 - 강제 넓은 범위
        self.y_ranges = [50.0] * 8  # 각 센서별 Y축 최대값 (매우 넓게 설정)
        self.max_values = [0.0] * 8  # 각 센서별 최대값 추적
        self.auto_scale_enabled = False  # 자동 스케일링 비활성화 (강제 제어)
        self.scale_margin = 3.0  # 스케일링 여유 공간 (300% 마진)
        self.min_y_range = 30.0  # 최소 Y축 범위 매우 크게 증가
        self.max_y_range = 200.0  # 최대 Y축 범위 매우 크게 증가
        self.range_update_threshold = 0.05  # 범위 업데이트 임계값 매우 민감하게

        # 드래그 안정성을 위한 변수들
        self.updating_threshold = [False] * 8  # 업데이트 중 플래그 (무한 루프 방지)
        self.last_threshold_update = [0.0] * 8  # 마지막 업데이트 시간
        self.threshold_update_cooldown = 0.05  # 50ms 쿨다운

        # 저장된 설정 불러오기
        self.load_settings()

        # UI 설정
        self.setWindowTitle("드럼 센서 모니터링 (10ms 고속 모드) - 키 매핑: a,s,d,f,j,k,l,;")
        self.resize(1400, 900)  # 창 크기 증가
        self.setup_ui()

        # 시리얼 포트 자동 감지
        self.detect_serial_ports()

        # 고속 타이머 설정
        self.timer = QtCore.QTimer()
        self.timer.timeout.connect(self.update_plots)
        self.timer.start(UPDATE_INTERVAL)

        # 성능 모니터링 타이머
        self.performance_timer = QtCore.QTimer()
        self.performance_timer.timeout.connect(self.update_performance_display)
        self.performance_timer.start(1000)  # 1초마다 성능 업데이트

        # 고성능 병렬 키 입력 스레드 풀 시작
        self.start_parallel_key_input_system()

    def start_parallel_key_input_system(self):
        """병렬 키 입력 처리 시스템 시작"""
        self.key_input_running = True

        # 다중 키 입력 처리 스레드 생성
        for i in range(self.max_key_threads):
            thread = threading.Thread(target=self.parallel_key_processor, args=(i,), daemon=True)
            thread.start()
            self.key_input_threads.append(thread)

        print(f"병렬 키 입력 시스템 시작됨 ({self.max_key_threads}개 스레드)")

    def parallel_key_processor(self, thread_id):
        """병렬 키 입력 처리기 - 각 스레드가 독립적으로 키 입력 처리"""
        while self.key_input_running:
            try:
                # 키 입력 요청 대기 (짧은 타임아웃으로 빠른 응답)
                key_data = self.key_input_queue.get(timeout=0.05)
                key, press_time, timestamp = key_data

                # 동시 입력 방지 체크 (같은 키가 이미 눌려있는지 확인)
                with self.key_lock:
                    if key in self.active_keys:
                        # 같은 키가 이미 활성화되어 있으면 무시
                        self.key_input_queue.task_done()
                        continue
                    else:
                        # 키를 활성 상태로 표시
                        self.active_keys.add(key)

                try:
                    # 즉시 키 누르기 (지연 없음)
                    pyautogui.keyDown(key)

                    # 키 누름 시간 대기
                    time.sleep(press_time)

                    # 키 떼기
                    pyautogui.keyUp(key)

                    print(f"스레드 {thread_id}: {key} 키 입력 완료 ({press_time:.3f}초)")

                finally:
                    # 키를 비활성 상태로 변경
                    with self.key_lock:
                        self.active_keys.discard(key)

                    # 큐에서 작업 완료 표시
                    self.key_input_queue.task_done()

            except queue.Empty:
                # 타임아웃 - 계속 진행
                continue
            except Exception as e:
                print(f"스레드 {thread_id} 키 입력 오류: {str(e)}")
                # 오류 발생 시에도 키 상태 정리
                with self.key_lock:
                    if 'key' in locals():
                        self.active_keys.discard(key)

    def queue_key_input(self, key, press_time):
        """키 입력을 큐에 추가 (즉시 처리용)"""
        try:
            # 현재 시간 스탬프 추가
            timestamp = time.time()

            # 큐가 가득 찬 경우 오래된 입력 제거 (더 적극적으로)
            removed_count = 0
            while self.key_input_queue.full() and removed_count < 10:
                try:
                    old_data = self.key_input_queue.get_nowait()
                    self.key_input_queue.task_done()
                    removed_count += 1
                except queue.Empty:
                    break

            if removed_count > 0:
                print(f"큐 정리: {removed_count}개 오래된 키 입력 제거")

            # 새 키 입력 추가 (타임스탬프 포함)
            self.key_input_queue.put((key, press_time, timestamp), block=False)
            return True

        except queue.Full:
            print(f"키 입력 큐 포화 - {key} 입력 무시")
            return False

    def load_settings(self):
        """저장된 설정 불러오기"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)

                    if 'thresholds' in config and len(config['thresholds']) == 8:
                        self.thresholds = config['thresholds']
                        print(f"임계값 불러옴: {self.thresholds}")

                    if 'background_mode' in config:
                        self.background_mode = config['background_mode']

                    if 'key_press_time' in config:
                        self.key_press_time = config['key_press_time']

                    if 'auto_scale_enabled' in config:
                        self.auto_scale_enabled = config['auto_scale_enabled']

                    if 'y_ranges' in config and len(config['y_ranges']) == 8:
                        self.y_ranges = config['y_ranges']

                print("설정을 성공적으로 불러왔습니다.")
            else:
                print("기본 설정을 사용합니다.")
        except Exception as e:
            print(f"설정 불러오기 오류: {str(e)}")

    def save_settings(self):
        """현재 설정 저장하기"""
        try:
            config = {
                'thresholds': self.thresholds.copy(),
                'background_mode': self.background_var.isChecked(),
                'key_press_time': self.key_press_time,
                'auto_scale_enabled': self.auto_scale_enabled,
                'y_ranges': self.y_ranges.copy()
            }

            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=4)

            print(f"설정을 성공적으로 저장했습니다: {self.config_file}")
        except Exception as e:
            print(f"설정 저장 오류: {str(e)}")

    def setup_ui(self):
        # 메인 위젯 및 레이아웃
        central_widget = QtWidgets.QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QtWidgets.QVBoxLayout(central_widget)

        # 상태 표시 패널
        status_panel = QtWidgets.QHBoxLayout()
        main_layout.addLayout(status_panel)

        # 성능 표시 레이블
        self.performance_label = QtWidgets.QLabel("데이터 속도: 0.0 Hz")
        self.performance_label.setStyleSheet("QLabel { color: green; font-weight: bold; }")
        status_panel.addWidget(self.performance_label)

        # 키 입력 상태 표시
        self.key_status_label = QtWidgets.QLabel("키 입력: 0개 대기")
        self.key_status_label.setStyleSheet("QLabel { color: blue; font-weight: bold; }")
        status_panel.addWidget(self.key_status_label)

        # 연결 상태 표시
        self.connection_status = QtWidgets.QLabel("연결 안됨")
        self.connection_status.setStyleSheet("QLabel { color: red; font-weight: bold; }")
        status_panel.addWidget(self.connection_status)

        status_panel.addStretch(1)

        # 컨트롤 패널
        control_panel = QtWidgets.QHBoxLayout()
        main_layout.addLayout(control_panel)

        # 시리얼 포트 선택
        control_panel.addWidget(QtWidgets.QLabel("시리얼 포트:"))
        self.port_combo = QtWidgets.QComboBox()
        control_panel.addWidget(self.port_combo)

        # 새로고침 버튼
        refresh_btn = QtWidgets.QPushButton("새로고침")
        refresh_btn.clicked.connect(self.detect_serial_ports)
        control_panel.addWidget(refresh_btn)

        # 연결/연결 해제 버튼
        self.connect_button = QtWidgets.QPushButton("연결")
        self.connect_button.clicked.connect(self.toggle_connection)
        control_panel.addWidget(self.connect_button)

        # 백그라운드 모드 체크박스
        self.background_var = QtWidgets.QCheckBox("백그라운드 모드")
        self.background_var.setChecked(self.background_mode)
        self.background_var.stateChanged.connect(self.on_background_mode_changed)
        control_panel.addWidget(self.background_var)

        # 자동 스케일링 체크박스
        self.auto_scale_var = QtWidgets.QCheckBox("자동 Y축 조정")
        self.auto_scale_var.setChecked(self.auto_scale_enabled)
        self.auto_scale_var.stateChanged.connect(self.on_auto_scale_changed)
        control_panel.addWidget(self.auto_scale_var)

        # Y축 범위 초기화 버튼
        reset_y_btn = QtWidgets.QPushButton("Y축 초기화")
        reset_y_btn.clicked.connect(self.reset_y_ranges)
        control_panel.addWidget(reset_y_btn)

        # 키 누름 시간 조절 UI
        key_press_time_layout = QtWidgets.QHBoxLayout()
        key_press_time_layout.addWidget(QtWidgets.QLabel("키 누름 시간:"))

        self.key_press_time_slider = QtWidgets.QSlider(QtCore.Qt.Horizontal)
        self.key_press_time_slider.setMinimum(int(MIN_KEY_PRESS_TIME * 1000))  # 50ms
        self.key_press_time_slider.setMaximum(int(MAX_KEY_PRESS_TIME * 1000))  # 300ms
        self.key_press_time_slider.setValue(int(self.key_press_time * 1000))
        self.key_press_time_slider.valueChanged.connect(self.update_key_press_time)
        key_press_time_layout.addWidget(self.key_press_time_slider)

        self.key_press_time_label = QtWidgets.QLabel(f"{self.key_press_time:.3f}초")
        key_press_time_layout.addWidget(self.key_press_time_label)

        control_panel.addLayout(key_press_time_layout)
        control_panel.addStretch(1)

        # 임계값 조절 패널
        threshold_panel = QtWidgets.QGroupBox("임계값 조절 (고속 모드) - 드래그 또는 정밀 조절")
        threshold_layout = QtWidgets.QHBoxLayout(threshold_panel)
        main_layout.addWidget(threshold_panel)

        # 각 센서별 임계값 슬라이더 및 스핀박스
        self.threshold_sliders = []
        self.threshold_labels = []
        self.threshold_spinboxes = []

        for i in range(8):
            sensor_frame = QtWidgets.QVBoxLayout()
            threshold_layout.addLayout(sensor_frame)

            sensor_frame.addWidget(QtWidgets.QLabel(f"센서 {i+1} [{KEY_MAPPING[i]}]"))

            value_label = QtWidgets.QLabel(f"{self.thresholds[i]:.2f}")  # 소수점 2자리로 정밀 표시
            sensor_frame.addWidget(value_label)
            self.threshold_labels.append(value_label)

            # 정밀 조절용 스핀박스 (마이너스 지원)
            spinbox = QtWidgets.QDoubleSpinBox()
            spinbox.setMinimum(-20.0)  # 최소값 (마이너스 지원)
            spinbox.setMaximum(20.0)  # 최대값
            spinbox.setSingleStep(0.01)  # 0.01g 단위 조절
            spinbox.setDecimals(2)  # 소수점 2자리
            spinbox.setValue(self.thresholds[i])
            spinbox.valueChanged.connect(lambda value, idx=i: self.update_threshold_precise(idx, value))
            sensor_frame.addWidget(spinbox)
            self.threshold_spinboxes.append(spinbox)

            slider = QtWidgets.QSlider(QtCore.Qt.Vertical)
            slider.setMinimum(-2000)  # -20.0g (마이너스 지원)
            slider.setMaximum(2000)  # 20.0g (더 넓은 범위)
            slider.setValue(int(self.thresholds[i] * 100))  # 0.01g 단위로 조절
            slider.valueChanged.connect(lambda value, idx=i: self.update_threshold(idx, value/100))
            sensor_frame.addWidget(slider)
            self.threshold_sliders.append(slider)

        # 그래프 레이아웃
        self.graph_layout = pg.GraphicsLayoutWidget()
        main_layout.addWidget(self.graph_layout, 1)

        # 그래프 설정
        self.setup_plots()

    def setup_plots(self):
        # 8개 센서용 그래프 생성
        self.plots = []
        self.curves = []
        self.threshold_lines = []
        self.hit_regions = []

        # 2x4 그리드로 그래프 배치
        for i in range(8):
            row = i // 4
            col = i % 4

            plot = self.graph_layout.addPlot(row=row, col=col, title=f'센서 {i+1} [{KEY_MAPPING[i]}] (100Hz)')

            # 강제로 매우 넓은 Y축 범위 설정 (자동 스케일링 완전 비활성화)
            plot.setYRange(-self.y_ranges[i], self.y_ranges[i])  # 대칭적 Y축 범위
            plot.setXRange(0, MAX_DATA_POINTS)
            plot.showGrid(x=True, y=True)

            # 자동 스케일링 완전 비활성화
            plot.enableAutoRange(axis='y', enable=False)
            plot.enableAutoRange(axis='x', enable=False)

            # Y축 범위 강제 고정
            plot.setYRange(-self.y_ranges[i], self.y_ranges[i])
            plot.setLimits(yMin=-self.y_ranges[i], yMax=self.y_ranges[i])  # 범위 제한 설정

            print(f"센서 {i+1} Y축 범위 강제 설정: ±{self.y_ranges[i]:.1f}g")

            # 고속 데이터용 곡선 (더 얇은 선)
            curve = plot.plot(pen=pg.mkPen('y', width=1))

            # 드래그 가능한 임계값 선 (마이너스 지원)
            threshold_line = pg.InfiniteLine(
                pos=self.thresholds[i],
                angle=0,
                pen=pg.mkPen('r', width=3, style=QtCore.Qt.DashLine),
                movable=True,  # 드래그 가능하게 설정
                bounds=[-20.0, 20.0],  # 드래그 범위 제한 (마이너스 지원)
                hoverPen=pg.mkPen('orange', width=4),  # 마우스 오버 시 색상 변경
                label=f"임계값: {self.thresholds[i]:.2f}g"  # 라벨 표시
            )

            # 드래그 시 임계값 업데이트 연결
            threshold_line.sigPositionChanged.connect(
                lambda line, idx=i: self.on_threshold_line_moved(idx, line.value())
            )

            plot.addItem(threshold_line)

            # 타격 표시용 영역
            hit_region = pg.LinearRegionItem(
                [0, MAX_DATA_POINTS],
                brush=pg.mkBrush(0, 255, 0, 80),
                movable=False
            )
            hit_region.setVisible(False)
            plot.addItem(hit_region)

            self.plots.append(plot)
            self.curves.append(curve)
            self.threshold_lines.append(threshold_line)
            self.hit_regions.append(hit_region)

    def detect_serial_ports(self):
        """사용 가능한 시리얼 포트 감지"""
        self.port_combo.clear()
        ports = [port.device for port in serial.tools.list_ports.comports()]

        for port in ports:
            self.port_combo.addItem(port)

        # NodeMCU 자동 감지
        found_nodemcu = False
        for port in serial.tools.list_ports.comports():
            if 'CH340' in port.description or 'USB-SERIAL' in port.description:
                self.port_combo.setCurrentText(port.device)
                found_nodemcu = True
                break

        if not found_nodemcu and ports:
            self.port_combo.setCurrentIndex(0)

    def toggle_connection(self):
        """연결/연결 해제 토글"""
        if not self.is_running:
            self.connect()
        else:
            self.disconnect()

    def connect(self):
        """시리얼 포트 연결 및 데이터 수신 시작"""
        port = self.port_combo.currentText()
        if not port:
            QtWidgets.QMessageBox.critical(self, "오류", "시리얼 포트를 선택하세요.")
            return

        try:
            self.serial_port = serial.Serial(port, 115200, timeout=SERIAL_TIMEOUT)
            self.is_running = True
            self.connect_button.setText("연결 해제")
            self.connection_status.setText("연결됨 (10ms 고속 모드)")
            self.connection_status.setStyleSheet("QLabel { color: green; font-weight: bold; }")

            # Y축 범위 초기화
            self.reset_y_ranges()

            # 고속 데이터 수신 스레드 시작
            self.receive_thread = threading.Thread(target=self.receive_data_highspeed)
            self.receive_thread.daemon = True
            self.receive_thread.start()

            QtWidgets.QMessageBox.information(self, "연결 성공", f"{port}에 연결되었습니다.\n10ms 고속 모드로 동작합니다.\n자동 Y축 조정: {'활성화' if self.auto_scale_enabled else '비활성화'}\nY축 범위: ±{self.min_y_range}g ~ ±{self.max_y_range}g")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "연결 오류", f"연결 중 오류 발생: {str(e)}")

    def disconnect(self):
        """연결 해제"""
        self.is_running = False
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()
        self.connect_button.setText("연결")
        self.connection_status.setText("연결 안됨")
        self.connection_status.setStyleSheet("QLabel { color: red; font-weight: bold; }")

    def receive_data_highspeed(self):
        """고속 시리얼 데이터 수신 스레드"""
        buffer = ""

        while self.is_running:
            try:
                if self.serial_port and self.serial_port.is_open:
                    # 더 많은 데이터를 한 번에 읽기
                    data = self.serial_port.read(self.serial_port.in_waiting or 1)
                    if data:
                        buffer += data.decode('utf-8', errors='ignore')

                        if '\n' in buffer:
                            lines = buffer.split('\n')
                            buffer = lines[-1]

                            for line in lines[:-1]:
                                self.process_data_highspeed(line.strip())

                # 고속 모드를 위한 최소 지연
                time.sleep(0.001)
            except Exception as e:
                print(f"고속 데이터 수신 오류: {str(e)}")
                time.sleep(0.01)

    def process_data_highspeed(self, data_line):
        """고속 데이터 처리"""
        try:
            parts = data_line.split(',')
            if len(parts) >= 8:
                self.sample_count += 1
                self.data_rate_counter += 1

                sensor_values = []
                for i in range(8):
                    try:
                        value = float(parts[i])
                        sensor_values.append(value)
                    except (ValueError, IndexError):
                        sensor_values.append(0.0)

                # 큐가 가득 찬 경우 오래된 데이터 제거
                if self.data_queue.full():
                    try:
                        self.data_queue.get_nowait()
                    except queue.Empty:
                        pass

                self.data_queue.put((self.sample_count, sensor_values))
                self.detect_hits_highspeed(sensor_values)
        except Exception as e:
            print(f"고속 데이터 처리 오류: {str(e)}")

    def detect_hits_highspeed(self, sensor_values):
        """고속 타격 감지"""
        current_time = time.time()

        for i, value in enumerate(sensor_values):
            if (value > self.thresholds[i] and
                current_time - self.last_trigger_time[i] > COOLDOWN_TIME):

                self.hit_indicators[i] = True
                self.last_trigger_time[i] = current_time

                if self.background_var.isChecked():
                    key = KEY_MAPPING[i]
                    try:
                        press_time = self.key_press_time

                        # 즉시 처리를 위한 병렬 키 입력 큐에 추가
                        if self.queue_key_input(key, press_time):
                            # 성공적으로 큐에 추가됨 - 로그는 처리 스레드에서 출력
                            pass
                        else:
                            print(f"병렬 키 입력 큐 포화 - {key} 무시")
                    except Exception as e:
                        print(f"병렬 키 입력 오류: {str(e)}")

    def update_performance_display(self):
        """성능 표시 업데이트"""
        current_time = time.time()
        time_diff = current_time - self.last_data_time

        if time_diff >= 1.0:
            self.actual_data_rate = self.data_rate_counter / time_diff
            self.performance_label.setText(f"데이터 속도: {self.actual_data_rate:.1f} Hz")

            # 성능에 따른 색상 변경
            if self.actual_data_rate >= 90:
                self.performance_label.setStyleSheet("QLabel { color: green; font-weight: bold; }")
            elif self.actual_data_rate >= 50:
                self.performance_label.setStyleSheet("QLabel { color: orange; font-weight: bold; }")
            else:
                self.performance_label.setStyleSheet("QLabel { color: red; font-weight: bold; }")

            self.data_rate_counter = 0
            self.last_data_time = current_time

        # 키 입력 상태 업데이트
        queue_size = self.key_input_queue.qsize()
        active_keys_count = len(self.active_keys)

        self.key_status_label.setText(f"키 입력: {queue_size}개 대기, {active_keys_count}개 활성")

        # 키 입력 상태에 따른 색상 변경
        if queue_size == 0 and active_keys_count == 0:
            self.key_status_label.setStyleSheet("QLabel { color: blue; font-weight: bold; }")
        elif queue_size < 10:
            self.key_status_label.setStyleSheet("QLabel { color: green; font-weight: bold; }")
        elif queue_size < 50:
            self.key_status_label.setStyleSheet("QLabel { color: orange; font-weight: bold; }")
        else:
            self.key_status_label.setStyleSheet("QLabel { color: red; font-weight: bold; }")

    def on_background_mode_changed(self, state):
        """백그라운드 모드 변경"""
        self.background_mode = bool(state)
        print(f"백그라운드 모드 변경: {self.background_mode}")
        self.delayed_save_settings()

    def on_auto_scale_changed(self, state):
        """자동 스케일링 모드 변경"""
        self.auto_scale_enabled = bool(state)
        print(f"자동 Y축 조정 변경: {self.auto_scale_enabled}")

        # 모든 그래프의 자동 스케일링 설정 업데이트 (강제 제어)
        for i, plot in enumerate(self.plots):
            if self.auto_scale_enabled:
                plot.enableAutoRange(axis='y', enable=True)
                print(f"센서 {i+1}: 자동 Y축 조정 활성화")
            else:
                # 강제로 자동 스케일링 비활성화 및 Y축 범위 설정
                plot.enableAutoRange(axis='y', enable=False)
                plot.setYRange(-self.y_ranges[i], self.y_ranges[i])
                plot.setLimits(yMin=-self.y_ranges[i], yMax=self.y_ranges[i])
                print(f"센서 {i+1}: 강제 고정 Y축 범위 ±{self.y_ranges[i]:.1f}g")

        self.delayed_save_settings()

    def update_y_range_dynamic(self, sensor_idx, value):
        """동적 Y축 범위 업데이트 - 강제 확장"""
        # 절댓값으로 최대값 추적
        abs_value = abs(value)

        # 현재 Y축 범위의 50%를 초과하면 즉시 확장 (매우 민감하게)
        current_range = self.y_ranges[sensor_idx]
        if abs_value > current_range * 0.5:
            # 새로운 Y축 범위 계산 (매우 큰 마진 적용)
            new_range = max(self.min_y_range,
                           min(self.max_y_range,
                               abs_value * self.scale_margin))

            # 매우 민감한 업데이트 (0.05g 이상 차이나면 업데이트)
            if abs(new_range - self.y_ranges[sensor_idx]) > self.range_update_threshold:
                self.y_ranges[sensor_idx] = new_range

                # 강제로 Y축 범위 설정 (자동 스케일링 무시)
                self.plots[sensor_idx].setYRange(-new_range, new_range)
                self.plots[sensor_idx].setLimits(yMin=-new_range, yMax=new_range)

                print(f"센서 {sensor_idx+1} Y축 범위 강제 확장: ±{new_range:.1f}g (값: {abs_value:.1f}g)")

        # 최대값 추적 및 강제 확장
        if abs_value > self.max_values[sensor_idx]:
            self.max_values[sensor_idx] = abs_value

            # 매우 적극적인 범위 확장
            new_range = max(self.min_y_range,
                           min(self.max_y_range,
                               abs_value * self.scale_margin))

            # 매우 민감한 업데이트
            if abs(new_range - self.y_ranges[sensor_idx]) > self.range_update_threshold:
                self.y_ranges[sensor_idx] = new_range

                # 강제로 Y축 범위 설정
                self.plots[sensor_idx].setYRange(-new_range, new_range)
                self.plots[sensor_idx].setLimits(yMin=-new_range, yMax=new_range)

                print(f"센서 {sensor_idx+1} Y축 범위 강제 확장: ±{new_range:.1f}g (최대값: {abs_value:.1f}g)")

        # 값이 작아도 최소 범위 보장 (강제)
        if abs_value > 1.0 and self.y_ranges[sensor_idx] < self.min_y_range:
            self.y_ranges[sensor_idx] = self.min_y_range
            self.plots[sensor_idx].setYRange(-self.min_y_range, self.min_y_range)
            self.plots[sensor_idx].setLimits(yMin=-self.min_y_range, yMax=self.min_y_range)
            print(f"센서 {sensor_idx+1} 최소 Y축 범위 강제 보장: ±{self.min_y_range:.1f}g")

    def reset_y_ranges(self):
        """Y축 범위 초기화 - 강제 넓은 기본 범위 (마이너스 지원)"""
        for i in range(8):
            # 임계값의 절댓값의 10배 또는 최소 범위 중 큰 값으로 설정 (매우 넓게)
            self.y_ranges[i] = max(self.min_y_range, abs(self.thresholds[i]) * 10.0)
            self.max_values[i] = 0.0

            # 강제로 Y축 범위 설정 (자동 스케일링 무시)
            self.plots[i].setYRange(-self.y_ranges[i], self.y_ranges[i])
            self.plots[i].setLimits(yMin=-self.y_ranges[i], yMax=self.y_ranges[i])
            self.plots[i].enableAutoRange(axis='y', enable=False)  # 자동 스케일링 강제 비활성화

            print(f"센서 {i+1} Y축 범위 강제 초기화: ±{self.y_ranges[i]:.1f}g")
        print(f"Y축 범위 초기화 완료 (기본 범위: ±{self.min_y_range}g ~ ±{self.max_y_range}g)")

    def update_key_press_time(self, value):
        """키 누름 시간 업데이트 (밀리초 단위)"""
        self.key_press_time = value / 1000.0
        self.key_press_time_label.setText(f"{self.key_press_time:.3f}초")
        self.delayed_save_settings()

    def update_threshold(self, sensor_idx, value):
        """임계값 업데이트 (슬라이더에서 호출) - 안전한 업데이트"""
        # 무한 루프 방지
        if self.updating_threshold[sensor_idx]:
            return

        # 쿨다운 체크
        current_time = time.time()
        if current_time - self.last_threshold_update[sensor_idx] < self.threshold_update_cooldown:
            return

        try:
            self.updating_threshold[sensor_idx] = True
            self.last_threshold_update[sensor_idx] = current_time

            self.thresholds[sensor_idx] = value
            self.threshold_labels[sensor_idx].setText(f"{value:.2f}")

            # 스핀박스 동기화 (시그널 차단)
            self.threshold_spinboxes[sensor_idx].blockSignals(True)
            self.threshold_spinboxes[sensor_idx].setValue(value)
            self.threshold_spinboxes[sensor_idx].blockSignals(False)

            # 그래프 선 동기화 (시그널 차단)
            self.threshold_lines[sensor_idx].blockSignals(True)
            self.threshold_lines[sensor_idx].setValue(value)
            self.threshold_lines[sensor_idx].label.setText(f"임계값: {value:.2f}g")
            self.threshold_lines[sensor_idx].blockSignals(False)

            # Y축 범위 조정
            self.adjust_y_range_for_threshold(sensor_idx, value)

            print(f"센서 {sensor_idx+1} 임계값 업데이트: {value:.2f}g (슬라이더)")
            self.delayed_save_settings()

        finally:
            self.updating_threshold[sensor_idx] = False

    def update_threshold_precise(self, sensor_idx, value):
        """임계값 정밀 업데이트 (스핀박스에서 호출) - 안전한 업데이트"""
        # 무한 루프 방지
        if self.updating_threshold[sensor_idx]:
            return

        # 쿨다운 체크
        current_time = time.time()
        if current_time - self.last_threshold_update[sensor_idx] < self.threshold_update_cooldown:
            return

        try:
            self.updating_threshold[sensor_idx] = True
            self.last_threshold_update[sensor_idx] = current_time

            self.thresholds[sensor_idx] = value
            self.threshold_labels[sensor_idx].setText(f"{value:.2f}")

            # 슬라이더 동기화 (시그널 차단)
            self.threshold_sliders[sensor_idx].blockSignals(True)
            self.threshold_sliders[sensor_idx].setValue(int(value * 100))
            self.threshold_sliders[sensor_idx].blockSignals(False)

            # 그래프 선 동기화 (시그널 차단)
            self.threshold_lines[sensor_idx].blockSignals(True)
            self.threshold_lines[sensor_idx].setValue(value)
            self.threshold_lines[sensor_idx].label.setText(f"임계값: {value:.2f}g")
            self.threshold_lines[sensor_idx].blockSignals(False)

            # Y축 범위 조정
            self.adjust_y_range_for_threshold(sensor_idx, value)

            print(f"센서 {sensor_idx+1} 임계값 정밀 업데이트: {value:.2f}g (스핀박스)")
            self.delayed_save_settings()

        finally:
            self.updating_threshold[sensor_idx] = False

    def on_threshold_line_moved(self, sensor_idx, value):
        """그래프에서 임계값 선이 드래그될 때 호출 - 안전한 업데이트"""
        # 무한 루프 방지 (가장 중요!)
        if self.updating_threshold[sensor_idx]:
            return

        # 쿨다운 체크 (드래그 시 과도한 이벤트 방지)
        current_time = time.time()
        if current_time - self.last_threshold_update[sensor_idx] < self.threshold_update_cooldown:
            return

        # 값 범위 제한 (마이너스 지원)
        value = max(-20.0, min(20.0, value))

        try:
            self.updating_threshold[sensor_idx] = True
            self.last_threshold_update[sensor_idx] = current_time

            self.thresholds[sensor_idx] = value
            self.threshold_labels[sensor_idx].setText(f"{value:.2f}")

            # 슬라이더 동기화 (시그널 차단으로 무한 루프 방지)
            self.threshold_sliders[sensor_idx].blockSignals(True)
            self.threshold_sliders[sensor_idx].setValue(int(value * 100))
            self.threshold_sliders[sensor_idx].blockSignals(False)

            # 스핀박스 동기화 (시그널 차단으로 무한 루프 방지)
            self.threshold_spinboxes[sensor_idx].blockSignals(True)
            self.threshold_spinboxes[sensor_idx].setValue(value)
            self.threshold_spinboxes[sensor_idx].blockSignals(False)

            # 임계값 선 라벨만 업데이트 (선 자체는 이미 이동됨)
            self.threshold_lines[sensor_idx].label.setText(f"임계값: {value:.2f}g")

            # Y축 범위 조정
            self.adjust_y_range_for_threshold(sensor_idx, value)

            print(f"센서 {sensor_idx+1} 임계값 업데이트: {value:.2f}g (드래그)")
            self.delayed_save_settings()

        except Exception as e:
            print(f"드래그 업데이트 오류: {e}")
        finally:
            self.updating_threshold[sensor_idx] = False

    def adjust_y_range_for_threshold(self, sensor_idx, value):
        """임계값 변경 시 Y축 범위 조정 (마이너스 지원) - 강제 설정"""
        # 임계값의 절댓값의 10배를 최소 Y축 범위로 설정 (매우 넓게)
        min_required_range = max(self.min_y_range, abs(value) * 10.0)
        if self.y_ranges[sensor_idx] < min_required_range:
            self.y_ranges[sensor_idx] = min_required_range

            # 강제로 Y축 범위 설정
            self.plots[sensor_idx].setYRange(-min_required_range, min_required_range)
            self.plots[sensor_idx].setLimits(yMin=-min_required_range, yMax=min_required_range)
            self.plots[sensor_idx].enableAutoRange(axis='y', enable=False)  # 자동 스케일링 강제 비활성화

            print(f"센서 {sensor_idx+1} Y축 범위 강제 조정: ±{min_required_range:.1f}g (임계값: {value:.2f}g)")

    def delayed_save_settings(self):
        """지연된 설정 저장"""
        if hasattr(self, '_save_timer'):
            self._save_timer.stop()
        else:
            self._save_timer = QtCore.QTimer()
            self._save_timer.setSingleShot(True)
            self._save_timer.timeout.connect(self.save_settings)
        self._save_timer.start(1000)

    def update_plots(self):
        """고속 그래프 업데이트"""
        updates_processed = 0
        max_updates_per_cycle = 50  # 한 번에 처리할 최대 업데이트 수

        while not self.data_queue.empty() and updates_processed < max_updates_per_cycle:
            try:
                sample_idx, values = self.data_queue.get_nowait()

                self.data_index = (self.data_index + 1) % MAX_DATA_POINTS
                self.sample_indices[self.data_index] = sample_idx

                for i, value in enumerate(values):
                    self.sensor_data[i][self.data_index] = value
                    # 동적 Y축 범위 업데이트
                    self.update_y_range_dynamic(i, value)

                updates_processed += 1
            except queue.Empty:
                break

        # 그래프 업데이트
        if updates_processed > 0:
            x = np.arange(MAX_DATA_POINTS)
            for i in range(8):
                rolled_data = np.roll(self.sensor_data[i], -self.data_index-1)
                self.curves[i].setData(x, rolled_data)

                if self.hit_indicators[i]:
                    self.hit_regions[i].setVisible(True)
                    self.hit_indicators[i] = False
                    QtCore.QTimer.singleShot(100, lambda idx=i: self.hide_hit_indicator(idx))

    def hide_hit_indicator(self, sensor_idx):
        """타격 표시 숨기기"""
        if sensor_idx < len(self.hit_regions):
            self.hit_regions[sensor_idx].setVisible(False)

    def closeEvent(self, event):
        """프로그램 종료 처리"""
        print("고속 드럼 센서 프로그램 종료 중...")

        if hasattr(self, '_save_timer') and self._save_timer.isActive():
            self._save_timer.stop()

        self.save_settings()
        self.is_running = False

        # 병렬 키 입력 스레드들 정리
        self.key_input_running = False
        if self.key_input_threads:
            print(f"병렬 키 입력 스레드 {len(self.key_input_threads)}개 종료 대기 중...")

            # 모든 스레드 종료 대기
            for i, thread in enumerate(self.key_input_threads):
                if thread.is_alive():
                    thread.join(timeout=0.5)  # 각 스레드당 0.5초 대기
                    if thread.is_alive():
                        print(f"키 입력 스레드 {i} 강제 종료")

            print("모든 병렬 키 입력 스레드 정리 완료")

        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()

        print("고속 드럼 센서 프로그램 종료 완료")
        event.accept()

if __name__ == "__main__":
    app = QtWidgets.QApplication(sys.argv)
    window = HighSpeedDrumSensorApp()
    window.show()
    sys.exit(app.exec_())
