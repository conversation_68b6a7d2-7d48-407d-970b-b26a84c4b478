"""
드럼 센서 모니터링 및 키 입력 시뮬레이션 프로그램 (10ms 고속 버전)

이 프로그램은 NodeMCU에서 전송하는 8개 센서 데이터를 10ms 간격으로 수신하여
실시간 그래프로 표시하고, 임계값을 초과하는 타격을 감지하여 키보드 입력을 시뮬레이션합니다.
PyQtGraph를 사용하여 하드웨어 가속을 지원하며, 100Hz 고속 데이터에 최적화되었습니다.
"""
import sys
import os
import time
import threading
import queue
import numpy as np
import serial
import serial.tools.list_ports
import pyautogui  # 키보드 입력 시뮬레이션용
import json  # 설정 저장 및 불러오기용

# PyQtGraph 설정
import pyqtgraph as pg
from pyqtgraph.Qt import QtCore, QtGui, QtWidgets

# 상수 정의 - 10ms 고속 모드에 최적화
MAX_DATA_POINTS = 200  # 그래프에 표시할 최대 데이터 포인트 수 (2초분)
DEFAULT_THRESHOLD = 1.2  # 기본 임계값 (g)
SERIAL_TIMEOUT = 0.01  # 시리얼 타임아웃 (10ms로 단축)
COOLDOWN_TIME = 0.05  # 타격 감지 후 쿨다운 시간 (50ms로 단축)
UPDATE_INTERVAL = 5  # 그래프 업데이트 간격 (5ms로 단축)
DEFAULT_KEY_PRESS_TIME = 0.1  # 기본 키 누름 시간 (100ms로 단축)
MIN_KEY_PRESS_TIME = 0.05  # 최소 키 누름 시간 (50ms)
MAX_KEY_PRESS_TIME = 0.3  # 최대 키 누름 시간 (300ms)

# 키 매핑 설정 (센서 인덱스 -> 키)
KEY_MAPPING = ['1', '2', '3', '4', '5', '6', '7', '8']

# 하드웨어 가속 활성화
pg.setConfigOptions(antialias=True, useOpenGL=True)

class HighSpeedDrumSensorApp(QtWidgets.QMainWindow):
    def __init__(self):
        super().__init__()

        # 설정 파일 경로
        try:
            if getattr(sys, 'frozen', False):
                application_path = os.path.dirname(sys.executable)
            else:
                application_path = os.path.dirname(os.path.abspath(__file__))

            self.config_file = os.path.join(application_path, 'drum_sensor_10ms_config.json')
            print(f"설정 파일 경로: {self.config_file}")
        except Exception as e:
            print(f"설정 파일 경로 설정 오류: {str(e)}")
            self.config_file = 'drum_sensor_10ms_config.json'

        # 변수 초기화 - 고속 모드에 최적화
        self.serial_port = None
        self.is_running = False
        self.data_queue = queue.Queue(maxsize=1000)  # 큐 크기 증가
        self.sensor_data = [np.zeros(MAX_DATA_POINTS) for _ in range(8)]
        self.sample_indices = np.arange(MAX_DATA_POINTS)
        self.thresholds = [DEFAULT_THRESHOLD] * 8
        self.last_trigger_time = [0] * 8
        self.hit_indicators = [False] * 8
        self.data_index = 0
        self.sample_count = 0
        self.background_mode = True
        self.key_press_time = DEFAULT_KEY_PRESS_TIME

        # 성능 모니터링 변수
        self.last_data_time = time.time()
        self.data_rate_counter = 0
        self.actual_data_rate = 0.0

        # 저장된 설정 불러오기
        self.load_settings()

        # UI 설정
        self.setWindowTitle("드럼 센서 모니터링 (10ms 고속 모드) - 키 매핑: 1,2,3,4,5,6,7,8")
        self.resize(1400, 900)  # 창 크기 증가
        self.setup_ui()

        # 시리얼 포트 자동 감지
        self.detect_serial_ports()

        # 고속 타이머 설정
        self.timer = QtCore.QTimer()
        self.timer.timeout.connect(self.update_plots)
        self.timer.start(UPDATE_INTERVAL)

        # 성능 모니터링 타이머
        self.performance_timer = QtCore.QTimer()
        self.performance_timer.timeout.connect(self.update_performance_display)
        self.performance_timer.start(1000)  # 1초마다 성능 업데이트

    def load_settings(self):
        """저장된 설정 불러오기"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)

                    if 'thresholds' in config and len(config['thresholds']) == 8:
                        self.thresholds = config['thresholds']
                        print(f"임계값 불러옴: {self.thresholds}")

                    if 'background_mode' in config:
                        self.background_mode = config['background_mode']

                    if 'key_press_time' in config:
                        self.key_press_time = config['key_press_time']

                print("설정을 성공적으로 불러왔습니다.")
            else:
                print("기본 설정을 사용합니다.")
        except Exception as e:
            print(f"설정 불러오기 오류: {str(e)}")

    def save_settings(self):
        """현재 설정 저장하기"""
        try:
            config = {
                'thresholds': self.thresholds.copy(),
                'background_mode': self.background_var.isChecked(),
                'key_press_time': self.key_press_time
            }

            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=4)

            print(f"설정을 성공적으로 저장했습니다: {self.config_file}")
        except Exception as e:
            print(f"설정 저장 오류: {str(e)}")

    def setup_ui(self):
        # 메인 위젯 및 레이아웃
        central_widget = QtWidgets.QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QtWidgets.QVBoxLayout(central_widget)

        # 상태 표시 패널
        status_panel = QtWidgets.QHBoxLayout()
        main_layout.addLayout(status_panel)

        # 성능 표시 레이블
        self.performance_label = QtWidgets.QLabel("데이터 속도: 0.0 Hz")
        self.performance_label.setStyleSheet("QLabel { color: green; font-weight: bold; }")
        status_panel.addWidget(self.performance_label)

        # 연결 상태 표시
        self.connection_status = QtWidgets.QLabel("연결 안됨")
        self.connection_status.setStyleSheet("QLabel { color: red; font-weight: bold; }")
        status_panel.addWidget(self.connection_status)

        status_panel.addStretch(1)

        # 컨트롤 패널
        control_panel = QtWidgets.QHBoxLayout()
        main_layout.addLayout(control_panel)

        # 시리얼 포트 선택
        control_panel.addWidget(QtWidgets.QLabel("시리얼 포트:"))
        self.port_combo = QtWidgets.QComboBox()
        control_panel.addWidget(self.port_combo)

        # 새로고침 버튼
        refresh_btn = QtWidgets.QPushButton("새로고침")
        refresh_btn.clicked.connect(self.detect_serial_ports)
        control_panel.addWidget(refresh_btn)

        # 연결/연결 해제 버튼
        self.connect_button = QtWidgets.QPushButton("연결")
        self.connect_button.clicked.connect(self.toggle_connection)
        control_panel.addWidget(self.connect_button)

        # 백그라운드 모드 체크박스
        self.background_var = QtWidgets.QCheckBox("백그라운드 모드")
        self.background_var.setChecked(self.background_mode)
        self.background_var.stateChanged.connect(self.on_background_mode_changed)
        control_panel.addWidget(self.background_var)

        # 키 누름 시간 조절 UI
        key_press_time_layout = QtWidgets.QHBoxLayout()
        key_press_time_layout.addWidget(QtWidgets.QLabel("키 누름 시간:"))

        self.key_press_time_slider = QtWidgets.QSlider(QtCore.Qt.Horizontal)
        self.key_press_time_slider.setMinimum(int(MIN_KEY_PRESS_TIME * 1000))  # 50ms
        self.key_press_time_slider.setMaximum(int(MAX_KEY_PRESS_TIME * 1000))  # 300ms
        self.key_press_time_slider.setValue(int(self.key_press_time * 1000))
        self.key_press_time_slider.valueChanged.connect(self.update_key_press_time)
        key_press_time_layout.addWidget(self.key_press_time_slider)

        self.key_press_time_label = QtWidgets.QLabel(f"{self.key_press_time:.3f}초")
        key_press_time_layout.addWidget(self.key_press_time_label)

        control_panel.addLayout(key_press_time_layout)
        control_panel.addStretch(1)

        # 임계값 조절 패널
        threshold_panel = QtWidgets.QGroupBox("임계값 조절 (고속 모드)")
        threshold_layout = QtWidgets.QHBoxLayout(threshold_panel)
        main_layout.addWidget(threshold_panel)

        # 각 센서별 임계값 슬라이더
        self.threshold_sliders = []
        self.threshold_labels = []

        for i in range(8):
            sensor_frame = QtWidgets.QVBoxLayout()
            threshold_layout.addLayout(sensor_frame)

            sensor_frame.addWidget(QtWidgets.QLabel(f"센서 {i+1} [{KEY_MAPPING[i]}]"))

            value_label = QtWidgets.QLabel(f"{self.thresholds[i]:.1f}")
            sensor_frame.addWidget(value_label)
            self.threshold_labels.append(value_label)

            slider = QtWidgets.QSlider(QtCore.Qt.Vertical)
            slider.setMinimum(1)  # 0.1g
            slider.setMaximum(50)  # 5.0g
            slider.setValue(int(self.thresholds[i] * 10))
            slider.valueChanged.connect(lambda value, idx=i: self.update_threshold(idx, value/10))
            sensor_frame.addWidget(slider)
            self.threshold_sliders.append(slider)

        # 그래프 레이아웃
        self.graph_layout = pg.GraphicsLayoutWidget()
        main_layout.addWidget(self.graph_layout, 1)

        # 그래프 설정
        self.setup_plots()

    def setup_plots(self):
        # 8개 센서용 그래프 생성
        self.plots = []
        self.curves = []
        self.threshold_lines = []
        self.hit_regions = []

        # 2x4 그리드로 그래프 배치
        for i in range(8):
            row = i // 4
            col = i % 4

            plot = self.graph_layout.addPlot(row=row, col=col, title=f'센서 {i+1} [{KEY_MAPPING[i]}] (100Hz)')
            plot.setYRange(0, 5)
            plot.setXRange(0, MAX_DATA_POINTS)
            plot.showGrid(x=True, y=True)

            # 고속 데이터용 곡선 (더 얇은 선)
            curve = plot.plot(pen=pg.mkPen('y', width=1))

            # 임계값 선
            threshold_line = pg.InfiniteLine(
                pos=self.thresholds[i],
                angle=0,
                pen=pg.mkPen('r', width=2, style=QtCore.Qt.DashLine),
                movable=False
            )
            plot.addItem(threshold_line)

            # 타격 표시용 영역
            hit_region = pg.LinearRegionItem(
                [0, MAX_DATA_POINTS],
                brush=pg.mkBrush(0, 255, 0, 80),
                movable=False
            )
            hit_region.setVisible(False)
            plot.addItem(hit_region)

            self.plots.append(plot)
            self.curves.append(curve)
            self.threshold_lines.append(threshold_line)
            self.hit_regions.append(hit_region)

    def detect_serial_ports(self):
        """사용 가능한 시리얼 포트 감지"""
        self.port_combo.clear()
        ports = [port.device for port in serial.tools.list_ports.comports()]

        for port in ports:
            self.port_combo.addItem(port)

        # NodeMCU 자동 감지
        found_nodemcu = False
        for port in serial.tools.list_ports.comports():
            if 'CH340' in port.description or 'USB-SERIAL' in port.description:
                self.port_combo.setCurrentText(port.device)
                found_nodemcu = True
                break

        if not found_nodemcu and ports:
            self.port_combo.setCurrentIndex(0)

    def toggle_connection(self):
        """연결/연결 해제 토글"""
        if not self.is_running:
            self.connect()
        else:
            self.disconnect()

    def connect(self):
        """시리얼 포트 연결 및 데이터 수신 시작"""
        port = self.port_combo.currentText()
        if not port:
            QtWidgets.QMessageBox.critical(self, "오류", "시리얼 포트를 선택하세요.")
            return

        try:
            self.serial_port = serial.Serial(port, 115200, timeout=SERIAL_TIMEOUT)
            self.is_running = True
            self.connect_button.setText("연결 해제")
            self.connection_status.setText("연결됨 (10ms 고속 모드)")
            self.connection_status.setStyleSheet("QLabel { color: green; font-weight: bold; }")

            # 고속 데이터 수신 스레드 시작
            self.receive_thread = threading.Thread(target=self.receive_data_highspeed)
            self.receive_thread.daemon = True
            self.receive_thread.start()

            QtWidgets.QMessageBox.information(self, "연결 성공", f"{port}에 연결되었습니다.\n10ms 고속 모드로 동작합니다.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "연결 오류", f"연결 중 오류 발생: {str(e)}")

    def disconnect(self):
        """연결 해제"""
        self.is_running = False
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()
        self.connect_button.setText("연결")
        self.connection_status.setText("연결 안됨")
        self.connection_status.setStyleSheet("QLabel { color: red; font-weight: bold; }")

    def receive_data_highspeed(self):
        """고속 시리얼 데이터 수신 스레드"""
        buffer = ""

        while self.is_running:
            try:
                if self.serial_port and self.serial_port.is_open:
                    # 더 많은 데이터를 한 번에 읽기
                    data = self.serial_port.read(self.serial_port.in_waiting or 1)
                    if data:
                        buffer += data.decode('utf-8', errors='ignore')

                        if '\n' in buffer:
                            lines = buffer.split('\n')
                            buffer = lines[-1]

                            for line in lines[:-1]:
                                self.process_data_highspeed(line.strip())

                # 고속 모드를 위한 최소 지연
                time.sleep(0.001)
            except Exception as e:
                print(f"고속 데이터 수신 오류: {str(e)}")
                time.sleep(0.01)

    def process_data_highspeed(self, data_line):
        """고속 데이터 처리"""
        try:
            parts = data_line.split(',')
            if len(parts) >= 8:
                self.sample_count += 1
                self.data_rate_counter += 1

                sensor_values = []
                for i in range(8):
                    try:
                        value = float(parts[i])
                        sensor_values.append(value)
                    except (ValueError, IndexError):
                        sensor_values.append(0.0)

                # 큐가 가득 찬 경우 오래된 데이터 제거
                if self.data_queue.full():
                    try:
                        self.data_queue.get_nowait()
                    except queue.Empty:
                        pass

                self.data_queue.put((self.sample_count, sensor_values))
                self.detect_hits_highspeed(sensor_values)
        except Exception as e:
            print(f"고속 데이터 처리 오류: {str(e)}")

    def detect_hits_highspeed(self, sensor_values):
        """고속 타격 감지"""
        current_time = time.time()

        for i, value in enumerate(sensor_values):
            if (value > self.thresholds[i] and
                current_time - self.last_trigger_time[i] > COOLDOWN_TIME):

                self.hit_indicators[i] = True
                self.last_trigger_time[i] = current_time

                if self.background_var.isChecked():
                    key = KEY_MAPPING[i]
                    try:
                        press_time = self.key_press_time
                        print(f"고속 키 입력: {key} ({press_time:.3f}초)")

                        pyautogui.keyDown(key)

                        def release_key(k):
                            time.sleep(press_time)
                            pyautogui.keyUp(k)

                        threading.Thread(target=release_key, args=(key,), daemon=True).start()
                    except Exception as e:
                        print(f"고속 키 입력 오류: {str(e)}")

    def update_performance_display(self):
        """성능 표시 업데이트"""
        current_time = time.time()
        time_diff = current_time - self.last_data_time

        if time_diff >= 1.0:
            self.actual_data_rate = self.data_rate_counter / time_diff
            self.performance_label.setText(f"데이터 속도: {self.actual_data_rate:.1f} Hz")

            # 성능에 따른 색상 변경
            if self.actual_data_rate >= 90:
                self.performance_label.setStyleSheet("QLabel { color: green; font-weight: bold; }")
            elif self.actual_data_rate >= 50:
                self.performance_label.setStyleSheet("QLabel { color: orange; font-weight: bold; }")
            else:
                self.performance_label.setStyleSheet("QLabel { color: red; font-weight: bold; }")

            self.data_rate_counter = 0
            self.last_data_time = current_time

    def on_background_mode_changed(self, state):
        """백그라운드 모드 변경"""
        self.background_mode = bool(state)
        print(f"백그라운드 모드 변경: {self.background_mode}")
        self.delayed_save_settings()

    def update_key_press_time(self, value):
        """키 누름 시간 업데이트 (밀리초 단위)"""
        self.key_press_time = value / 1000.0
        self.key_press_time_label.setText(f"{self.key_press_time:.3f}초")
        self.delayed_save_settings()

    def update_threshold(self, sensor_idx, value):
        """임계값 업데이트"""
        self.thresholds[sensor_idx] = value
        self.threshold_labels[sensor_idx].setText(f"{value:.1f}")
        self.threshold_lines[sensor_idx].setValue(value)
        self.delayed_save_settings()

    def delayed_save_settings(self):
        """지연된 설정 저장"""
        if hasattr(self, '_save_timer'):
            self._save_timer.stop()
        else:
            self._save_timer = QtCore.QTimer()
            self._save_timer.setSingleShot(True)
            self._save_timer.timeout.connect(self.save_settings)
        self._save_timer.start(1000)

    def update_plots(self):
        """고속 그래프 업데이트"""
        updates_processed = 0
        max_updates_per_cycle = 50  # 한 번에 처리할 최대 업데이트 수

        while not self.data_queue.empty() and updates_processed < max_updates_per_cycle:
            try:
                sample_idx, values = self.data_queue.get_nowait()

                self.data_index = (self.data_index + 1) % MAX_DATA_POINTS
                self.sample_indices[self.data_index] = sample_idx

                for i, value in enumerate(values):
                    self.sensor_data[i][self.data_index] = value

                updates_processed += 1
            except queue.Empty:
                break

        # 그래프 업데이트
        if updates_processed > 0:
            x = np.arange(MAX_DATA_POINTS)
            for i in range(8):
                rolled_data = np.roll(self.sensor_data[i], -self.data_index-1)
                self.curves[i].setData(x, rolled_data)

                if self.hit_indicators[i]:
                    self.hit_regions[i].setVisible(True)
                    self.hit_indicators[i] = False
                    QtCore.QTimer.singleShot(100, lambda idx=i: self.hide_hit_indicator(idx))

    def hide_hit_indicator(self, sensor_idx):
        """타격 표시 숨기기"""
        if sensor_idx < len(self.hit_regions):
            self.hit_regions[sensor_idx].setVisible(False)

    def closeEvent(self, event):
        """프로그램 종료 처리"""
        print("고속 드럼 센서 프로그램 종료 중...")

        if hasattr(self, '_save_timer') and self._save_timer.isActive():
            self._save_timer.stop()

        self.save_settings()
        self.is_running = False

        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()

        print("고속 드럼 센서 프로그램 종료 완료")
        event.accept()

if __name__ == "__main__":
    app = QtWidgets.QApplication(sys.argv)
    window = HighSpeedDrumSensorApp()
    window.show()
    sys.exit(app.exec_())
