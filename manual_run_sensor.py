#!/usr/bin/env python3
"""
Manually run the sensor program
"""
import serial
import time

def manual_run_sensor(port):
    try:
        print("수동으로 센서 프로그램 실행...")
        ser = serial.Serial(port, 115200, timeout=10)
        time.sleep(2)
        
        # 현재 프로그램 중단
        for i in range(3):
            ser.write(b'\x03')
            time.sleep(0.5)
        
        # 버퍼 클리어
        ser.read_all()
        
        # main.py 실행
        print("main.py 실행 중...")
        ser.write(b'exec(open("main.py").read())\r\n')
        time.sleep(2)
        
        # 실행 결과 읽기
        print("센서 프로그램 출력:")
        start_time = time.time()
        
        while time.time() - start_time < 15:
            if ser.in_waiting > 0:
                try:
                    line = ser.readline().decode('utf-8', errors='ignore').strip()
                    if line:
                        print(f"  {line}")
                        # 센서 데이터가 나오기 시작하면 계속 읽기
                        if ',' in line and line.count(',') == 7:
                            print("센서 데이터 수신 중... (Ctrl+C로 중단)")
                            # 추가로 몇 줄 더 읽기
                            for _ in range(10):
                                if ser.in_waiting > 0:
                                    line = ser.readline().decode('utf-8', errors='ignore').strip()
                                    if line:
                                        print(f"  {line}")
                                time.sleep(0.1)
                            break
                except:
                    pass
            time.sleep(0.1)
        
        ser.close()
        
    except Exception as e:
        print(f"오류: {e}")

if __name__ == "__main__":
    manual_run_sensor("COM14")
