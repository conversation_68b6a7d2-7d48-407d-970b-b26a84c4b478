"""
Real sensor program speed test
Test actual sensor performance at different I2C frequencies
"""
import time
from machine import I2C, Pin, freq
import gc

# Configuration
I2C_SCL_PIN = 5  # D1 on NodeMCU
I2C_SDA_PIN = 4  # D2 on NodeMCU
TCA9548A_ADDRESS = 0x70
MPU6050_ADDRESS = 0x68
SAMPLE_RATE_MS = 25
NUM_SENSORS = 8

# Test frequencies (will be set by parameter)
TEST_FREQUENCY = 100000  # Default 100kHz

def safe_print(msg):
    try:
        print(msg)
    except:
        pass

def setup_with_frequency(test_freq):
    """Setup I2C with specific frequency"""
    try:
        freq(160000000)  # Full CPU speed for testing
        safe_print(f"Testing I2C at {test_freq}Hz ({test_freq/1000:.0f}kHz)")
        
        i2c = I2C(scl=Pin(I2C_SCL_PIN), sda=Pin(I2C_SDA_PIN), freq=test_freq)
        time.sleep_ms(500)
        
        devices = i2c.scan()
        safe_print(f"I2C devices: {[hex(d) for d in devices]}")
        
        return i2c
    except Exception as e:
        safe_print(f"Setup error: {e}")
        return None

def select_channel(i2c, channel):
    """Select TCA9548A channel"""
    try:
        i2c.writeto(TCA9548A_ADDRESS, bytes([1 << channel]))
        time.sleep_ms(10)
        return True
    except:
        return False

def initialize_mpu6050(i2c, channel):
    """Initialize MPU6050 sensor"""
    try:
        if not select_channel(i2c, channel):
            return False
        
        devices = i2c.scan()
        if MPU6050_ADDRESS not in devices:
            return False
        
        # Wake up
        i2c.writeto_mem(MPU6050_ADDRESS, 0x6B, b'\x00')
        time.sleep_ms(50)
        
        # Configure
        i2c.writeto_mem(MPU6050_ADDRESS, 0x1C, b'\x00')  # ±2g
        time.sleep_ms(50)
        
        return True
    except:
        return False

def read_accel_z(i2c, channel):
    """Read Z-axis acceleration"""
    try:
        if not select_channel(i2c, channel):
            return 0.0
        
        data = i2c.readfrom_mem(MPU6050_ADDRESS, 0x3F, 2)
        z = (data[0] << 8) | data[1]
        if z > 32767:
            z -= 65536
        z_g = z / 16384.0
        return z_g
    except:
        return 0.0

def test_frequency_performance(test_freq):
    """Test sensor performance at specific frequency"""
    try:
        safe_print(f"\n=== Testing {test_freq}Hz ({test_freq/1000:.0f}kHz) ===")
        
        # Setup
        i2c = setup_with_frequency(test_freq)
        if i2c is None:
            safe_print(f"Failed to initialize I2C at {test_freq}Hz")
            return False
        
        # Check TCA9548A
        devices = i2c.scan()
        if TCA9548A_ADDRESS not in devices:
            safe_print(f"TCA9548A not found at {test_freq}Hz")
            return False
        
        # Initialize sensors
        connected_sensors = [False] * NUM_SENSORS
        safe_print("Initializing sensors...")
        
        for channel in range(NUM_SENSORS):
            if initialize_mpu6050(i2c, channel):
                connected_sensors[channel] = True
                safe_print(f"✅ Sensor {channel + 1} OK")
            else:
                safe_print(f"❌ Sensor {channel + 1} FAIL")
        
        total_connected = sum(connected_sensors)
        safe_print(f"Connected sensors: {total_connected}/8")
        
        if total_connected == 0:
            safe_print(f"No sensors at {test_freq}Hz")
            return False
        
        # Test data reading
        safe_print("Testing data reading...")
        safe_print("sensor1,sensor2,sensor3,sensor4,sensor5,sensor6,sensor7,sensor8")
        
        error_count = 0
        read_count = 0
        
        for i in range(20):  # Test 20 readings
            values = []
            for channel in range(NUM_SENSORS):
                if connected_sensors[channel]:
                    z_g = read_accel_z(i2c, channel)
                    if z_g == 0.0:
                        error_count += 1
                    read_count += 1
                else:
                    z_g = 0.0
                values.append(f"{z_g:.3f}")
            
            print(f"{','.join(values)}")
            time.sleep_ms(SAMPLE_RATE_MS)
        
        # Calculate performance
        if read_count > 0:
            error_rate = (error_count / read_count) * 100
            success_rate = 100 - error_rate
        else:
            success_rate = 0
        
        safe_print(f"\n{test_freq}Hz Performance:")
        safe_print(f"  Sensors: {total_connected}/8")
        safe_print(f"  Success rate: {success_rate:.1f}%")
        safe_print(f"  Errors: {error_count}/{read_count}")
        
        # Performance rating
        if total_connected == 8 and success_rate >= 95:
            safe_print(f"🏆 {test_freq}Hz: EXCELLENT")
            return True
        elif total_connected >= 6 and success_rate >= 90:
            safe_print(f"✅ {test_freq}Hz: GOOD")
            return True
        elif total_connected >= 4 and success_rate >= 80:
            safe_print(f"⚠️ {test_freq}Hz: FAIR")
            return True
        else:
            safe_print(f"❌ {test_freq}Hz: POOR")
            return False
        
    except Exception as e:
        safe_print(f"Test error at {test_freq}Hz: {e}")
        return False

def main():
    """Main speed testing function"""
    try:
        safe_print("=== I2C Speed Performance Test ===")
        safe_print("Testing real sensor performance at different speeds")
        
        # Test frequencies from low to high
        test_frequencies = [50000, 100000, 200000, 400000, 800000]
        
        results = {}
        
        for test_freq in test_frequencies:
            success = test_frequency_performance(test_freq)
            results[test_freq] = success
            
            # Wait between tests
            time.sleep_ms(2000)
            gc.collect()
        
        # Summary
        safe_print(f"\n=== SPEED TEST SUMMARY ===")
        working_freqs = []
        for freq, success in results.items():
            status = "✅ PASS" if success else "❌ FAIL"
            safe_print(f"{freq:>6}Hz ({freq/1000:>3.0f}kHz): {status}")
            if success:
                working_freqs.append(freq)
        
        if working_freqs:
            max_freq = max(working_freqs)
            safe_print(f"\n🏆 Maximum stable frequency: {max_freq}Hz ({max_freq/1000:.0f}kHz)")
        else:
            safe_print(f"\n❌ No stable frequencies found")
        
    except Exception as e:
        safe_print(f"Main error: {e}")

if __name__ == "__main__":
    main()
