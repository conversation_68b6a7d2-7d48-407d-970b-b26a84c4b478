#!/usr/bin/env python3
"""
Check MPU6050 addresses on each TCA9548A channel
"""
import serial
import time

def check_sensor_addresses():
    try:
        print("각 채널별 MPU6050 주소 확인...")
        ser = serial.Serial("COM14", 115200, timeout=10)
        time.sleep(2)
        
        # 프로그램 중단
        for i in range(3):
            ser.write(b'\x03')
            time.sleep(0.5)
        
        ser.read_all()
        
        # I2C 초기화
        commands = [
            "from machine import I2C, Pin",
            "i2c = I2C(scl=Pin(5), sda=Pin(4), freq=100000)",
            "print('I2C initialized')"
        ]
        
        for cmd in commands:
            ser.write(f"{cmd}\r\n".encode('utf-8'))
            time.sleep(1)
        
        response = ser.read_all().decode('utf-8', errors='ignore')
        print(f"초기화: {response}")
        
        # 각 채널별 주소 확인
        print("\n=== 각 채널별 I2C 주소 스캔 ===")
        for channel in range(8):
            print(f"\n--- 채널 {channel} ---")
            
            scan_commands = [
                f"# 채널 {channel} 선택",
                f"try:",
                f"    i2c.writeto(0x70, bytes([{1 << channel}]))",
                f"    import time",
                f"    time.sleep_ms(50)",
                f"    devices = i2c.scan()",
                f"    print('Channel {channel} devices:', [hex(d) for d in devices])",
                f"    # MPU6050 주소 확인",
                f"    mpu_addresses = [d for d in devices if d in [0x68, 0x69]]",
                f"    if mpu_addresses:",
                f"        print('Channel {channel} MPU6050 addresses:', [hex(addr) for addr in mpu_addresses])",
                f"        for addr in mpu_addresses:",
                f"            try:",
                f"                who_am_i = i2c.readfrom_mem(addr, 0x75, 1)",
                f"                print(f'Address {{hex(addr)}} WHO_AM_I: {{hex(who_am_i[0])}}')",
                f"            except Exception as e:",
                f"                print(f'Address {{hex(addr)}} read error: {{e}}')",
                f"    else:",
                f"        print('Channel {channel}: No MPU6050 found')",
                f"except Exception as e:",
                f"    print('Channel {channel} error:', e)"
            ]
            
            for cmd in scan_commands:
                if cmd.startswith('#'):
                    continue
                ser.write(f"{cmd}\r\n".encode('utf-8'))
                time.sleep(0.2)
            
            # 결과 읽기
            time.sleep(2)
            response = ser.read_all().decode('utf-8', errors='ignore')
            if response.strip():
                lines = response.split('\n')
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('>>>') and not line.startswith('...'):
                        print(f"  {line}")
        
        # 전체 요약
        print("\n=== 전체 I2C 주소 스캔 (TCA9548A 없이) ===")
        summary_commands = [
            "# 전체 I2C 버스 스캔",
            "try:",
            "    all_devices = i2c.scan()",
            "    print('All I2C devices:', [hex(d) for d in all_devices])",
            "    mpu_count_68 = all_devices.count(0x68)",
            "    mpu_count_69 = all_devices.count(0x69)",
            "    print(f'MPU6050 at 0x68: {mpu_count_68} devices')",
            "    print(f'MPU6050 at 0x69: {mpu_count_69} devices')",
            "except Exception as e:",
            "    print('Scan error:', e)"
        ]
        
        for cmd in summary_commands:
            if cmd.startswith('#'):
                continue
            ser.write(f"{cmd}\r\n".encode('utf-8'))
            time.sleep(0.2)
        
        time.sleep(2)
        response = ser.read_all().decode('utf-8', errors='ignore')
        if response.strip():
            lines = response.split('\n')
            for line in lines:
                line = line.strip()
                if line and not line.startswith('>>>') and not line.startswith('...'):
                    print(f"  {line}")
        
        ser.close()
        
    except Exception as e:
        print(f"오류: {e}")

if __name__ == "__main__":
    check_sensor_addresses()
