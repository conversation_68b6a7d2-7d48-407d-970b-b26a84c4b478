#!/usr/bin/env python3
"""
Test very low I2C frequencies for maximum sensor detection
"""
import serial
import time

def test_very_low_frequencies():
    try:
        print("매우 낮은 I2C 주파수 테스트...")
        ser = serial.Serial("COM14", 115200, timeout=20)
        time.sleep(2)
        
        # 프로그램 중단
        for i in range(3):
            ser.write(b'\x03')
            time.sleep(0.5)
        
        ser.read_all()
        
        # 매우 낮은 주파수들부터 테스트 (1kHz부터 시작)
        frequencies = [1000, 2000, 5000, 10000, 20000, 30000]
        
        print("=== 매우 낮은 I2C 주파수 테스트 ===")
        
        best_freq = 0
        max_sensors = 0
        
        for freq in frequencies:
            print(f"\n{'='*50}")
            print(f"🔍 {freq}Hz ({freq/1000:.1f}kHz) 테스트")
            print(f"{'='*50}")
            
            try:
                # I2C 초기화
                init_commands = [
                    "from machine import I2C, Pin",
                    f"i2c = I2C(scl=Pin(5), sda=Pin(4), freq={freq})",
                    f"print('I2C at {freq}Hz ready')",
                    "import time"
                ]
                
                for cmd in init_commands:
                    ser.write(f"{cmd}\r\n".encode('utf-8'))
                    time.sleep(0.5)
                
                response = ser.read_all().decode('utf-8', errors='ignore')
                print(f"초기화: {response.strip()}")
                
                # 전체 스캔
                ser.write(b'all_devices = i2c.scan()\r\n')
                time.sleep(2)  # 낮은 주파수이므로 더 긴 대기
                ser.write(b'print("All devices:", [hex(d) for d in all_devices])\r\n')
                time.sleep(2)
                
                response = ser.read_all().decode('utf-8', errors='ignore')
                print(f"전체 스캔: {response.strip()}")
                
                # TCA9548A 확인
                ser.write(b'tca_found = 0x70 in all_devices\r\n')
                ser.write(b'print("TCA9548A found:", tca_found)\r\n')
                time.sleep(1)
                
                response = ser.read_all().decode('utf-8', errors='ignore')
                print(f"TCA9548A: {response.strip()}")
                
                # 각 채널별 센서 확인 (매우 천천히)
                print("채널별 센서 확인 중...")
                
                channel_commands = [
                    "found_sensors = []",
                    "for ch in range(8):",
                    "    try:",
                    "        print(f'Checking channel {ch}...')",
                    "        i2c.writeto(0x70, bytes([1 << ch]))",
                    "        time.sleep_ms(200)",  # 더 긴 대기
                    "        ch_dev = i2c.scan()",
                    "        if 0x68 in ch_dev:",
                    "            found_sensors.append(ch)",
                    "            print(f'✅ Channel {ch}: MPU6050 found')",
                    "        else:",
                    "            print(f'❌ Channel {ch}: No MPU6050')",
                    "    except Exception as e:",
                    "        print(f'Channel {ch} error: {e}')",
                    "    time.sleep_ms(100)",
                    f"print(f'🎯 {freq}Hz total sensors: {{len(found_sensors)}}')",
                    f"print(f'📍 {freq}Hz sensor channels: {{found_sensors}}')"
                ]
                
                for cmd in channel_commands:
                    ser.write(f"{cmd}\r\n".encode('utf-8'))
                    time.sleep(0.1)
                
                # 결과 읽기 (충분한 시간 대기)
                time.sleep(10)  # 8개 채널 * 약 1초씩
                response = ser.read_all().decode('utf-8', errors='ignore')
                
                # 결과 파싱
                lines = response.split('\n')
                sensor_count = 0
                found_channels = []
                
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('>>>') and not line.startswith('...'):
                        print(f"  {line}")
                        
                        if "total sensors:" in line:
                            try:
                                sensor_count = int(line.split(":")[-1].strip())
                            except:
                                pass
                        elif "sensor channels:" in line:
                            try:
                                channels_str = line.split(":")[-1].strip()
                                # [0, 2, 3] 형태에서 숫자 추출
                                import re
                                numbers = re.findall(r'\d+', channels_str)
                                found_channels = [int(n) for n in numbers]
                            except:
                                pass
                
                print(f"\n📊 {freq}Hz 최종 결과:")
                print(f"   감지된 센서: {sensor_count}개")
                print(f"   활성 채널: {found_channels}")
                
                # 최고 성능 기록
                if sensor_count > max_sensors:
                    max_sensors = sensor_count
                    best_freq = freq
                    print(f"🏆 새로운 최고 기록! {freq}Hz에서 {sensor_count}개 센서")
                
            except Exception as e:
                print(f"❌ {freq}Hz 테스트 실패: {e}")
            
            time.sleep(2)
        
        print(f"\n{'='*50}")
        print(f"🏆 최종 결과")
        print(f"{'='*50}")
        print(f"최적 주파수: {best_freq}Hz ({best_freq/1000:.1f}kHz)")
        print(f"최대 센서 개수: {max_sensors}개")
        
        if best_freq > 0:
            print(f"\n💡 권장사항: {best_freq}Hz로 센서 프로그램을 실행하세요!")
        
        ser.close()
        
    except Exception as e:
        print(f"오류: {e}")

if __name__ == "__main__":
    test_very_low_frequencies()
