"""
Simple script to read Z-axis data from MPU6050 sensors through TCA9548A multiplexer
Displays data as text in the terminal
"""
import time
from machine import I2C, Pin

# Configuration
I2C_SCL_PIN = 5  # D1 on NodeMCU
I2C_SDA_PIN = 4  # D2 on NodeMCU
TCA9548A_ADDRESS = 0x70  # Default address of TCA9548A
MPU6050_ADDRESS = 0x68   # Default address of MPU6050

# Initialize I2C
i2c = I2C(scl=Pin(I2C_SCL_PIN), sda=Pin(I2C_SDA_PIN), freq=400000)

# Function to select TCA9548A channel
def select_channel(channel):
    if not 0 <= channel <= 7:
        raise ValueError("Channel must be between 0 and 7")
    i2c.writeto(TCA9548A_ADDRESS, bytes([1 << channel]))

# Function to read Z-axis acceleration from MPU6050
def read_accel_z(channel):
    # Select the channel
    select_channel(channel)
    time.sleep(0.001)  # Small delay for channel switching
    
    # Wake up the MPU6050 (in case it's in sleep mode)
    i2c.writeto_mem(MPU6050_ADDRESS, 0x6B, b'\x00')
    time.sleep(0.001)
    
    # Read Z-axis acceleration registers
    data = i2c.readfrom_mem(MPU6050_ADDRESS, 0x3F, 2)
    
    # Convert to 16-bit signed value
    z = (data[0] << 8) | data[1]
    if z > 32767:
        z -= 65536
    
    # Convert to g (±2g range)
    z_g = z / 16384.0
    
    return z_g

# Scan I2C bus
devices = i2c.scan()
print("I2C devices found:", [hex(device) for device in devices])

# Check if TCA9548A is found
if TCA9548A_ADDRESS not in devices:
    print("TCA9548A not found. Check your wiring.")
else:
    print("TCA9548A found at address 0x70")
    
    # Scan each channel for MPU6050 sensors
    connected_sensors = []
    for channel in range(8):
        select_channel(channel)
        time.sleep(0.1)
        
        channel_devices = i2c.scan()
        print(f"Channel {channel} devices: {[hex(device) for device in channel_devices]}")
        
        if MPU6050_ADDRESS in channel_devices:
            print(f"MPU6050 found on channel {channel}")
            connected_sensors.append(channel)
    
    if not connected_sensors:
        print("No MPU6050 sensors found. Check your wiring.")
    else:
        print(f"Found {len(connected_sensors)} MPU6050 sensors on channels: {connected_sensors}")
        
        # Continuously read and display Z-axis data
        print("\nReading Z-axis acceleration data (Ctrl+C to stop)...")
        print("channel,z_acceleration")
        
        try:
            while True:
                for channel in connected_sensors:
                    try:
                        z_g = read_accel_z(channel)
                        print(f"{channel+1},{z_g:.3f}")
                    except Exception as e:
                        print(f"Error reading channel {channel}: {e}")
                
                time.sleep(0.1)  # 10 readings per second
        except KeyboardInterrupt:
            print("Stopped by user")
